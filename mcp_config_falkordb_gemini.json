{"mcpServers": {"graphiti-falkordb-gemini": {"transport": "stdio", "command": "python", "args": ["/root/graphiti-mcp-server/graphiti_mcp_server_falkordb_gemini.py", "--transport", "stdio"], "env": {"GOOGLE_API_KEY": "AIzaSyCYRDGkPcemu4cK3Nu-4zIuslNJBeqhPxE", "FALKORDB_HOST": "localhost", "FALKORDB_PORT": "6379", "FALKORDB_DATABASE": "graphiti_db", "GEMINI_MODEL": "gemini-2.5-flash", "GEMINI_SMALL_MODEL": "gemini-2.5-flash-lite-preview-06-17", "GEMINI_EMBEDDING_MODEL": "text-embedding-001", "SEMAPHORE_LIMIT": "5", "STORE_RAW_EPISODE_CONTENT": "true", "MAX_COROUTINES": "10", "LOG_LEVEL": "INFO", "GRAPHITI_TELEMETRY_ENABLED": "false"}}}}