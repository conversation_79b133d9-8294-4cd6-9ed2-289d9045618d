# FalkorDB 和 Gemini 配置文件
# 复制此文件为 .env 并填入实际值

# =============================================================================
# Gemini API 配置
# =============================================================================
# 您的 Google Gemini API 密钥
GOOGLE_API_KEY=AIzaSyCYRDGkPcemu4cK3Nu-4zIuslNJBeqhPxE

# Gemini 模型配置
GEMINI_MODEL=gemini-2.5-flash
GEMINI_SMALL_MODEL=gemini-2.5-flash-lite-preview-06-17
GEMINI_EMBEDDING_MODEL=text-embedding-001

# =============================================================================
# FalkorDB 配置
# =============================================================================
# FalkorDB 连接参数
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_USERNAME=
FALKORDB_PASSWORD=
FALKORDB_DATABASE=graphiti_db

# =============================================================================
# Graphiti 性能配置
# =============================================================================
# 并发限制（避免 API 速率限制）
SEMAPHORE_LIMIT=5

# 是否启用并行运行时（FalkorDB 不支持，保持 false）
USE_PARALLEL_RUNTIME=false

# =============================================================================
# 可选配置
# =============================================================================
# 日志级别
LOG_LEVEL=INFO

# 是否存储原始剧集内容
STORE_RAW_EPISODE_CONTENT=true

# 最大协程数
MAX_COROUTINES=10

# 禁用遥测（可选）
GRAPHITI_TELEMETRY_ENABLED=false
