以下是为您整理的 Graphiti 容器化部署指南 (集成 Google Gemini) ，涵盖完整安装流程、配置说明及验证步骤：

一、核心架构与前置条件

1. 系统架构  
   • Neo4j 容器：存储知识图谱数据（版本 ≥5.26）。  

   • Graphiti MCP 容器：提供动态图谱管理接口，需集成 Gemini SDK。  

   • Gemini API 密钥：用于大模型推理与嵌入生成（替代默认 OpenAI）。  

2. 环境要求  
   • Docker 与 Docker Compose  

   • Google API 密钥（需开通 Gemini API 权限）  

   • 端口可用性：7687 (Neo4j)、8000 (Graphiti API)

二、容器化部署步骤

1. 创建 Dockerfile 定制 Graphiti 镜像

# 基于官方 Graphiti MCP 镜像
FROM getzep/graphiti-mcp:latest

# 安装 Gemini 支持扩展
RUN pip install "graphiti-core[google-genai]"


2. 编写 docker-compose.yml

version: '3.8'
services:
  neo4j:
    image: neo4j:5.26-enterprise
    container_name: neo4j
    ports:
      - "7687:7687"  # Bolt 协议
      - "7474:7474"  # Neo4j 浏览器
    volumes:
      - neo4j_data:/data
    environment:
      NEO4J_AUTH: neo4j/your_neo4j_password  # 替换实际密码
      NEO4J_ACCEPT_LICENSE_AGREEMENT: "yes"

  graphiti:
    build: .  # 指向包含 Dockerfile 的目录
    container_name: graphiti-mcp
    ports:
      - "8000:8000"  # MCP API 端口
    environment:
      # Neo4j 连接配置
      NEO4J_URI: "bolt://neo4j:7687"
      NEO4J_USERNAME: "neo4j"
      NEO4J_PASSWORD: "your_neo4j_password"  # 与上方一致
      
      # Gemini 配置 (替换 YOUR_GEMINI_API_KEY)
      LLM_PROVIDER: "google-genai"
      GEMINI_API_KEY: "YOUR_GEMINI_API_KEY"
      GEMINI_MODEL: "gemini-2.0-flash"          # 推理模型
      EMBEDDING_MODEL: "embedding-001"          # 嵌入模型
    depends_on:
      neo4j:
        condition: service_healthy

volumes:
  neo4j_data:


3. 启动服务

docker-compose up -d --build


三、关键配置说明

1. Gemini 模型选择  
   • 推理模型：gemini-2.0-flash（低延迟）或 gemini-2.5（高精度）。  

   • 嵌入模型：固定为 embedding-001。  

2. 安全增强建议  
   • 密钥管理：使用 Docker Secrets 或 Kubernetes Secrets 替代明文环境变量。  

   • 网络隔离：将 neo4j 和 graphiti 放入独立 Docker 网络，仅开放必要端口。  

3. 初始化延迟处理  
   Neo4j 首次启动需 1-2 分钟初始化，若 Graphiti 启动失败，重启即可：  
   docker-compose restart graphiti
   

四、功能验证

1. 检查服务状态

# 检查 Neo4j
curl http://localhost:7474  # 应返回 Neo4j 浏览器界面

# 检查 Graphiti
curl http://localhost:8000/health 
# 预期返回：{"status":"OK","llm_provider":"google-genai"} 


2. 测试 Gemini 集成

通过 Python 脚本验证实体提取能力：  
import asyncio
from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient

async def test_gemini():
    graphiti = Graphiti(
        "bolt://localhost:7687", 
        "neo4j", 
        "your_neo4j_password",
        llm_client=GeminiClient(api_key="YOUR_GEMINI_API_KEY")
    )
    await graphiti.add_episode(
        name="测试事件",
        episode_body="用户预算5000元，想购买华为手机",
        source={"type": "test"}
    )
    results = await graphiti.search_nodes(query="华为手机", limit=3)
    print("检索结果:", results)  # 应返回"华为"相关实体

asyncio.run(test_gemini())


五、故障排查

问题现象 解决方案

Gemini 实体提取失败 升级至 gemini-2.5 模型，或在配置中增加 temperature=0.3。

API 连接超时 检查防火墙规则，确保容器可访问 generativelanguage.googleapis.com:443。

图谱更新延迟 确认 USE_PARALLEL_RUNTIME=true 已设置（仅限 Neo4j Enterprise 版本）。

启动时 Neo4j 连接失败 添加健康检查重试机制：在 docker-compose.yml 中为 Graphiti 增加 restart: on-failure。

六、生产环境建议

1. 持久化存储  
   挂载 Neo4j 数据卷至持久化存储（如 AWS EBS、NFS）。  
2. 监控与日志  
   • 集成 Prometheus 收集 Neo4j/Gemini API 指标。  

   • 使用 ELK 栈集中管理容器日志。  

3. 高可用部署  
   • Neo4j：部署因果集群（Causal Cluster）。  

   • Graphiti：通过 Kubernetes 部署多副本，并配置负载均衡。  

通过此方案，您已获得支持 动态知识更新 与 Gemini 智能推理 的 Graphiti 容器化环境。进一步优化可参考 https://help.getzep.com/graphiti。