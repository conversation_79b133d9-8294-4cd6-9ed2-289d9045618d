#!/usr/bin/env python3
"""
FalkorDB + Gemini 测试服务器
在端口 8089 上运行 HTTP 服务器，提供 Web 界面测试 Graphiti 功能
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from aiohttp import web, web_request
    from aiohttp.web import middleware
except ImportError:
    print("❌ 需要安装 aiohttp")
    print("运行: pip install aiohttp")
    sys.exit(1)

from falkordb_gemini_config import get_configured_graphiti, FalkorDBGeminiConfig
from graphiti_core.nodes import EpisodeType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局 Graphiti 实例
graphiti_instance = None

class GraphitiTestServer:
    """Graphiti 测试服务器"""
    
    def __init__(self):
        self.app = web.Application(middlewares=[self.cors_middleware])
        self.graphiti = None
        self.setup_routes()
    
    @middleware
    async def cors_middleware(self, request, handler):
        """CORS 中间件"""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    def setup_routes(self):
        """设置路由"""
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_post('/add_episode', self.add_episode)
        self.app.router.add_post('/search', self.search)
        self.app.router.add_get('/stats', self.get_stats)
        self.app.router.add_options('/{path:.*}', self.options_handler)
    
    async def options_handler(self, request):
        """处理 OPTIONS 请求"""
        return web.Response(status=200)
    
    async def initialize(self):
        """初始化 Graphiti"""
        try:
            logger.info("🚀 初始化 Graphiti (FalkorDB + Gemini)...")
            self.graphiti = get_configured_graphiti()
            await self.graphiti.build_indices_and_constraints()
            logger.info("✅ Graphiti 初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ Graphiti 初始化失败: {e}")
            return False
    
    async def index(self, request):
        """主页"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti FalkorDB + Gemini 测试界面</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        .panel h2 { margin-top: 0; color: #333; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .stats { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🚀 Graphiti FalkorDB + Gemini 测试界面</h1>
    
    <div id="status" class="status"></div>
    
    <div class="stats" id="stats">
        <h3>📊 系统状态</h3>
        <div id="stats-content">加载中...</div>
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>📝 添加剧集</h2>
            <input type="text" id="episode-name" placeholder="剧集名称" style="width: 100%; margin: 10px 0;">
            <textarea id="episode-content" placeholder="剧集内容（文本或JSON）"></textarea>
            <select id="episode-type" style="width: 100%; margin: 10px 0;">
                <option value="text">文本</option>
                <option value="json">JSON</option>
            </select>
            <input type="text" id="episode-description" placeholder="剧集描述（可选）" style="width: 100%; margin: 10px 0;">
            <button onclick="addEpisode()">添加剧集</button>
            <div id="add-result"></div>
        </div>
        
        <div class="panel">
            <h2>🔍 搜索知识</h2>
            <input type="text" id="search-query" placeholder="搜索查询" style="width: 100%; margin: 10px 0;">
            <input type="number" id="search-limit" placeholder="结果数量" value="5" style="width: 100%; margin: 10px 0;">
            <button onclick="searchKnowledge()">搜索</button>
            <div id="search-result"></div>
        </div>
    </div>
    
    <script>
        // 检查服务器状态
        async function checkStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();
                const statusDiv = document.getElementById('status');
                if (data.status === 'ready') {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ 服务器就绪 - FalkorDB + Gemini 配置正常';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ 服务器未就绪: ' + data.message;
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ 无法连接到服务器';
            }
        }
        
        // 获取统计信息
        async function loadStats() {
            try {
                const response = await fetch('/stats');
                const data = await response.json();
                document.getElementById('stats-content').innerHTML = data.stats;
            } catch (error) {
                document.getElementById('stats-content').innerHTML = '无法加载统计信息';
            }
        }
        
        // 添加剧集
        async function addEpisode() {
            const name = document.getElementById('episode-name').value;
            const content = document.getElementById('episode-content').value;
            const type = document.getElementById('episode-type').value;
            const description = document.getElementById('episode-description').value;
            
            if (!name || !content) {
                alert('请填写剧集名称和内容');
                return;
            }
            
            try {
                const response = await fetch('/add_episode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name, content, type, description })
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('add-result');
                
                if (data.success) {
                    resultDiv.innerHTML = '<div class="result">' + data.message + '</div>';
                    // 清空表单
                    document.getElementById('episode-name').value = '';
                    document.getElementById('episode-content').value = '';
                    document.getElementById('episode-description').value = '';
                    // 刷新统计信息
                    loadStats();
                } else {
                    resultDiv.innerHTML = '<div class="result" style="background: #f8d7da;">❌ ' + data.message + '</div>';
                }
            } catch (error) {
                document.getElementById('add-result').innerHTML = '<div class="result" style="background: #f8d7da;">❌ 请求失败</div>';
            }
        }
        
        // 搜索知识
        async function searchKnowledge() {
            const query = document.getElementById('search-query').value;
            const limit = parseInt(document.getElementById('search-limit').value) || 5;
            
            if (!query) {
                alert('请输入搜索查询');
                return;
            }
            
            try {
                const response = await fetch('/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query, limit })
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('search-result');
                
                if (data.success) {
                    resultDiv.innerHTML = '<div class="result">' + data.results + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result" style="background: #f8d7da;">❌ ' + data.message + '</div>';
                }
            } catch (error) {
                document.getElementById('search-result').innerHTML = '<div class="result" style="background: #f8d7da;">❌ 搜索失败</div>';
            }
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
            loadStats();
        };
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def status(self, request):
        """状态检查"""
        if self.graphiti:
            return web.json_response({
                'status': 'ready',
                'message': 'Graphiti 已初始化',
                'config': {
                    'database': 'FalkorDB',
                    'llm': 'Gemini',
                    'embedder': 'Gemini'
                }
            })
        else:
            return web.json_response({
                'status': 'error',
                'message': 'Graphiti 未初始化'
            })
    
    async def add_episode(self, request):
        """添加剧集"""
        try:
            data = await request.json()
            name = data.get('name')
            content = data.get('content')
            episode_type = data.get('type', 'text')
            description = data.get('description', '')
            
            if not name or not content:
                return web.json_response({
                    'success': False,
                    'message': '缺少必需参数'
                })
            
            # 确定剧集类型
            if episode_type == 'json':
                ep_type = EpisodeType.json
                # 验证 JSON 格式
                try:
                    json.loads(content)
                except json.JSONDecodeError:
                    return web.json_response({
                        'success': False,
                        'message': '无效的JSON格式'
                    })
            else:
                ep_type = EpisodeType.text
            
            # 添加剧集
            result = await self.graphiti.add_episode(
                name=name,
                episode_body=content,
                source=ep_type,
                source_description=description,
                reference_time=datetime.now(timezone.utc)
            )
            
            message = f"✅ 成功添加剧集: {name}<br>"
            message += f"📊 提取的节点数: {len(result.nodes)}<br>"
            message += f"🔗 提取的边数: {len(result.edges)}<br>"
            message += f"🏘️ 社区数: {len(result.communities)}"
            
            if result.nodes:
                message += "<br><br>📝 提取的主要节点:<br>"
                for node in result.nodes[:3]:
                    summary = node.summary[:80] + "..." if len(node.summary) > 80 else node.summary
                    message += f"• {node.name}: {summary}<br>"
            
            return web.json_response({
                'success': True,
                'message': message
            })
            
        except Exception as e:
            logger.error(f"添加剧集失败: {e}")
            return web.json_response({
                'success': False,
                'message': f'添加剧集失败: {str(e)}'
            })
    
    async def search(self, request):
        """搜索"""
        try:
            data = await request.json()
            query = data.get('query')
            limit = data.get('limit', 5)
            
            if not query:
                return web.json_response({
                    'success': False,
                    'message': '缺少搜索查询'
                })
            
            # 执行搜索
            results = await self.graphiti.search(query=query, limit=limit)
            
            if not results:
                return web.json_response({
                    'success': True,
                    'results': f"未找到与 '{query}' 相关的结果"
                })
            
            response_html = f"🔍 搜索结果 ('{query}'):<br><br>"
            
            for i, result in enumerate(results, 1):
                response_html += f"<strong>{i}. {result.fact}</strong><br>"
                if hasattr(result, 'valid_at') and result.valid_at:
                    response_html += f"📅 有效期: {result.valid_at}<br>"
                response_html += f"🆔 UUID: {result.uuid}<br><br>"
            
            return web.json_response({
                'success': True,
                'results': response_html
            })
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return web.json_response({
                'success': False,
                'message': f'搜索失败: {str(e)}'
            })
    
    async def get_stats(self, request):
        """获取统计信息"""
        try:
            # 执行统计查询
            stats_query = "MATCH (n) RETURN count(n) as total_nodes"
            stats_result = await self.graphiti.driver.execute_query(stats_query)
            
            edges_query = "MATCH ()-[r]->() RETURN count(r) as total_edges"
            edges_result = await self.graphiti.driver.execute_query(edges_query)
            
            stats_html = "🗄️ <strong>数据库</strong>: FalkorDB<br>"
            stats_html += f"🤖 <strong>LLM</strong>: Gemini ({os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')})<br>"
            stats_html += f"🔤 <strong>嵌入模型</strong>: {os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001')}<br>"
            
            if stats_result and stats_result[0]:
                stats = stats_result[0][0]
                stats_html += f"📊 <strong>总节点数</strong>: {stats.get('total_nodes', 0)}<br>"
            
            if edges_result and edges_result[0]:
                edges = edges_result[0][0]
                stats_html += f"🔗 <strong>总边数</strong>: {edges.get('total_edges', 0)}<br>"
            
            return web.json_response({
                'success': True,
                'stats': stats_html
            })
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return web.json_response({
                'success': False,
                'stats': f"获取统计信息时出错: {str(e)}"
            })

async def main():
    """主函数"""
    logger.info("🚀 启动 Graphiti 测试服务器 (端口 8089)")
    
    # 创建服务器实例
    server = GraphitiTestServer()
    
    # 初始化 Graphiti
    if not await server.initialize():
        logger.error("❌ 无法初始化 Graphiti，服务器将无法正常工作")
        return
    
    try:
        # 启动 Web 服务器
        runner = web.AppRunner(server.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', 8089)
        await site.start()
        
        logger.info("✅ 服务器已启动")
        logger.info("🌐 访问地址: http://localhost:8089")
        logger.info("📋 使用 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器错误: {e}")
    finally:
        if server.graphiti:
            await server.graphiti.close()
        logger.info("✅ 服务器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
