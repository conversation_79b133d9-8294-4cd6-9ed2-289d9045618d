#!/usr/bin/env python3
"""
MCP 客户端示例
演示如何通过 SSE 和 MCP 协议与 Graphiti 服务器交互
"""

import asyncio
import json
import logging
import aiohttp
from datetime import datetime
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GraphitiMCPClient:
    """Graphiti MCP 客户端"""
    
    def __init__(self, server_url: str = "http://localhost:8089"):
        self.server_url = server_url
        self.sse_url = f"{server_url}/sse"
        self.mcp_url = f"{server_url}/mcp"
        self.session = None
        self.sse_task = None
        self.request_id = 0
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.sse_task:
            self.sse_task.cancel()
            try:
                await self.sse_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
    
    def get_next_request_id(self) -> str:
        """获取下一个请求 ID"""
        self.request_id += 1
        return f"req-{self.request_id}"
    
    async def connect_sse(self, message_handler=None):
        """连接到 SSE 端点"""
        logger.info(f"📡 连接到 SSE: {self.sse_url}")
        
        try:
            async with self.session.get(self.sse_url) as response:
                if response.status != 200:
                    logger.error(f"❌ SSE 连接失败: {response.status}")
                    return
                
                logger.info("✅ SSE 连接已建立")
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        try:
                            data = json.loads(data_str)
                            logger.info(f"📨 收到 SSE 消息: {data.get('type', 'unknown')}")
                            
                            if message_handler:
                                await message_handler(data)
                            else:
                                await self.default_message_handler(data)
                                
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ JSON 解析失败: {e}")
                            
        except asyncio.CancelledError:
            logger.info("📡 SSE 连接已取消")
        except Exception as e:
            logger.error(f"❌ SSE 连接错误: {e}")
    
    async def default_message_handler(self, data: Dict[str, Any]):
        """默认消息处理器"""
        msg_type = data.get('type', 'unknown')
        timestamp = data.get('timestamp', datetime.now().isoformat())
        
        if msg_type == 'connection_established':
            client_id = data.get('client_id')
            server_info = data.get('server_info', {})
            logger.info(f"🔗 连接已建立，客户端 ID: {client_id}")
            logger.info(f"🗄️ 服务器信息: {server_info}")
            
        elif msg_type == 'heartbeat':
            logger.debug(f"💓 心跳: {timestamp}")
            
        elif msg_type == 'episode_added':
            episode_data = data.get('data', {})
            logger.info(f"📚 新剧集已添加: {episode_data.get('name')}")
            logger.info(f"   节点数: {episode_data.get('nodes_count', 0)}")
            logger.info(f"   边数: {episode_data.get('edges_count', 0)}")
            
        elif msg_type == 'knowledge_search':
            search_data = data.get('data', {})
            logger.info(f"🔍 知识搜索: '{search_data.get('query')}'")
            logger.info(f"   结果数: {search_data.get('results_count', 0)}")
            
        elif msg_type == 'mcp_request':
            method = data.get('method')
            logger.info(f"📨 MCP 请求: {method}")
            
        elif msg_type == 'mcp_response':
            method = data.get('method')
            success = data.get('success', False)
            status = "✅" if success else "❌"
            logger.info(f"📤 MCP 响应: {method} {status}")
            
        else:
            logger.info(f"📋 未知消息类型: {msg_type}")
    
    async def send_mcp_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送 MCP 请求"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self.get_next_request_id(),
            "method": method,
            "params": params or {}
        }
        
        logger.info(f"📤 发送 MCP 请求: {method}")
        
        try:
            async with self.session.post(
                self.mcp_url,
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                if response.status != 200:
                    logger.error(f"❌ MCP 请求失败: {response.status}")
                    return {"error": f"HTTP {response.status}"}
                
                result = await response.json()
                logger.info(f"📨 收到 MCP 响应: {method}")
                return result
                
        except Exception as e:
            logger.error(f"❌ MCP 请求错误: {e}")
            return {"error": str(e)}
    
    async def initialize(self) -> Dict[str, Any]:
        """初始化 MCP 连接"""
        return await self.send_mcp_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "graphiti-mcp-client",
                "version": "1.0.0"
            }
        })
    
    async def list_tools(self) -> Dict[str, Any]:
        """列出可用工具"""
        return await self.send_mcp_request("tools/list")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        return await self.send_mcp_request("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
    
    async def add_episode(self, name: str, content: str, episode_type: str = "text", description: str = "") -> Dict[str, Any]:
        """添加剧集"""
        return await self.call_tool("add_episode", {
            "name": name,
            "content": content,
            "episode_type": episode_type,
            "description": description
        })
    
    async def search_knowledge(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """搜索知识"""
        return await self.call_tool("search_knowledge", {
            "query": query,
            "limit": limit
        })
    
    async def get_graph_stats(self) -> Dict[str, Any]:
        """获取图统计信息"""
        return await self.call_tool("get_graph_stats", {})

async def demo_client():
    """演示客户端使用"""
    logger.info("🚀 启动 Graphiti MCP 客户端演示")
    
    async with GraphitiMCPClient() as client:
        # 启动 SSE 连接（后台任务）
        client.sse_task = asyncio.create_task(client.connect_sse())
        
        # 等待 SSE 连接建立
        await asyncio.sleep(2)
        
        # 初始化 MCP 连接
        logger.info("\n📋 初始化 MCP 连接...")
        init_result = await client.initialize()
        logger.info(f"初始化结果: {init_result.get('result', {}).get('serverInfo', {})}")
        
        # 列出可用工具
        logger.info("\n🔧 获取可用工具...")
        tools_result = await client.list_tools()
        if 'result' in tools_result:
            tools = tools_result['result'].get('tools', [])
            logger.info(f"可用工具数量: {len(tools)}")
            for tool in tools:
                logger.info(f"  - {tool['name']}: {tool['description']}")
        
        # 获取图统计信息
        logger.info("\n📊 获取图统计信息...")
        stats_result = await client.get_graph_stats()
        if 'result' in stats_result:
            content = stats_result['result'].get('content', [])
            if content:
                logger.info(f"统计信息:\n{content[0]['text']}")
        
        # 添加示例剧集
        logger.info("\n📚 添加示例剧集...")
        episode_result = await client.add_episode(
            name="MCP 客户端测试",
            content="这是一个通过 MCP 客户端添加的测试剧集。它演示了如何使用 SSE 和 MCP 协议与 Graphiti 服务器进行交互。",
            episode_type="text",
            description="MCP 客户端演示剧集"
        )
        
        if 'result' in episode_result:
            content = episode_result['result'].get('content', [])
            if content:
                logger.info(f"添加结果:\n{content[0]['text']}")
        
        # 搜索知识
        logger.info("\n🔍 搜索知识...")
        search_result = await client.search_knowledge("MCP 客户端", limit=3)
        if 'result' in search_result:
            content = search_result['result'].get('content', [])
            if content:
                logger.info(f"搜索结果:\n{content[0]['text']}")
        
        # 保持连接一段时间以观察 SSE 消息
        logger.info("\n⏰ 保持连接 10 秒以观察实时消息...")
        await asyncio.sleep(10)
        
        logger.info("✅ 演示完成")

async def main():
    """主函数"""
    try:
        await demo_client()
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，退出演示")
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
