#!/usr/bin/env python3
"""
Graphiti SSE MCP 服务器
在端口 8089 上运行，支持 SSE 协议和 MCP 客户端连接
使用 FalkorDB + Gemini 配置
"""

import asyncio
import json
import logging
import os
import sys
import uuid
import signal
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
from weakref import WeakSet

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from aiohttp import web, WSMsgType
    from aiohttp.web import middleware
except ImportError:
    print("❌ 需要安装 aiohttp")
    print("运行: pip install aiohttp 或 uv add aiohttp")
    sys.exit(1)

try:
    from falkordb_gemini_config import get_configured_graphiti
    from graphiti_core.nodes import EpisodeType
    from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
except ImportError as e:
    print(f"❌ 无法导入 Graphiti 模块: {e}")
    print("请确保已安装 graphiti-core 和相关依赖")
    print("运行: pip install 'graphiti-core[falkordb,google-genai]'")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SSEConnection:
    """SSE 连接管理类"""
    
    def __init__(self, response: web.StreamResponse, client_id: str):
        self.response = response
        self.client_id = client_id
        self.connected = True
        self.last_heartbeat = datetime.now()
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """发送 SSE 消息"""
        if not self.connected:
            return False
        
        try:
            data = json.dumps(message, ensure_ascii=False, default=str)
            await self.response.write(f"data: {data}\n\n".encode('utf-8'))
            return True
        except Exception as e:
            logger.warning(f"⚠️ 向客户端 {self.client_id} 发送消息失败: {e}")
            self.connected = False
            return False
    
    async def send_heartbeat(self):
        """发送心跳"""
        self.last_heartbeat = datetime.now()
        return await self.send_message({
            "type": "heartbeat",
            "timestamp": self.last_heartbeat.isoformat()
        })

class GraphitiSSEMCPServer:
    """Graphiti SSE MCP 服务器主类"""
    
    def __init__(self):
        self.app = web.Application(middlewares=[self.cors_middleware])
        self.graphiti = None
        self.sse_connections: Dict[str, SSEConnection] = {}
        self.shutdown_event = asyncio.Event()
        self.heartbeat_task = None
        self.setup_routes()
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        for sig in (signal.SIGTERM, signal.SIGINT):
            signal.signal(sig, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"📡 收到信号 {signum}，准备关闭服务器...")
        self.shutdown_event.set()
    
    @middleware
    async def cors_middleware(self, request, handler):
        """CORS 中间件"""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Cache-Control, Accept'
        response.headers['Access-Control-Expose-Headers'] = 'Content-Type'
        return response
    
    def setup_routes(self):
        """设置路由"""
        # SSE 和 MCP 端点
        self.app.router.add_get('/sse', self.sse_handler)
        self.app.router.add_post('/mcp', self.mcp_handler)
        
        # 管理端点
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/clients', self.list_clients)
        
        # 测试端点
        self.app.router.add_post('/test/add_episode', self.test_add_episode)
        self.app.router.add_post('/test/search', self.test_search)
        
        # OPTIONS 处理
        self.app.router.add_options('/{path:.*}', self.options_handler)
    
    async def options_handler(self, request):
        """处理 OPTIONS 请求"""
        return web.Response(status=200)
    
    async def initialize(self):
        """初始化服务器"""
        try:
            logger.info("🚀 初始化 Graphiti (FalkorDB + Gemini)...")
            self.graphiti = get_configured_graphiti()
            await self.graphiti.build_indices_and_constraints()
            logger.info("✅ Graphiti 初始化完成")
            
            # 启动心跳任务
            self.heartbeat_task = asyncio.create_task(self.heartbeat_loop())
            logger.info("💓 心跳任务已启动")
            
            return True
        except Exception as e:
            logger.error(f"❌ 服务器初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 开始清理资源...")
        
        # 停止心跳任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有 SSE 连接
        for connection in list(self.sse_connections.values()):
            connection.connected = False
        self.sse_connections.clear()
        
        # 关闭 Graphiti
        if self.graphiti:
            await self.graphiti.close()
            logger.info("✅ Graphiti 连接已关闭")
        
        logger.info("✅ 资源清理完成")
    
    async def heartbeat_loop(self):
        """心跳循环"""
        while not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(30)  # 每30秒发送心跳
                
                # 清理断开的连接
                disconnected = []
                for client_id, connection in self.sse_connections.items():
                    if not connection.connected:
                        disconnected.append(client_id)
                    else:
                        success = await connection.send_heartbeat()
                        if not success:
                            disconnected.append(client_id)
                
                # 移除断开的连接
                for client_id in disconnected:
                    if client_id in self.sse_connections:
                        del self.sse_connections[client_id]
                        logger.info(f"🔌 移除断开的客户端: {client_id}")
                
                if self.sse_connections:
                    logger.debug(f"💓 心跳发送完成，活跃连接: {len(self.sse_connections)}")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 心跳循环错误: {e}")
    
    async def sse_handler(self, request):
        """SSE 连接处理器"""
        response = web.StreamResponse()
        response.headers['Content-Type'] = 'text/event-stream'
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        response.headers['Access-Control-Allow-Origin'] = '*'
        
        await response.prepare(request)
        
        # 创建客户端连接
        client_id = str(uuid.uuid4())
        connection = SSEConnection(response, client_id)
        self.sse_connections[client_id] = connection
        
        logger.info(f"📡 新的 SSE 客户端连接: {client_id}")
        
        try:
            # 发送欢迎消息
            await connection.send_message({
                "type": "connection_established",
                "client_id": client_id,
                "server_info": {
                    "name": "Graphiti SSE MCP Server",
                    "version": "1.0.0",
                    "database": "FalkorDB",
                    "llm": "Gemini",
                    "port": 8089,
                    "protocols": ["SSE", "MCP"]
                },
                "timestamp": datetime.now().isoformat()
            })
            
            # 保持连接直到客户端断开或服务器关闭
            while connection.connected and not self.shutdown_event.is_set():
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            logger.info(f"📡 SSE 客户端连接被取消: {client_id}")
        except Exception as e:
            logger.error(f"❌ SSE 连接错误 {client_id}: {e}")
        finally:
            connection.connected = False
            if client_id in self.sse_connections:
                del self.sse_connections[client_id]
            logger.info(f"📡 SSE 客户端断开连接: {client_id}")
        
        return response
    
    async def broadcast_to_sse_clients(self, message: Dict[str, Any]):
        """向所有 SSE 客户端广播消息"""
        if not self.sse_connections:
            return
        
        message["timestamp"] = datetime.now().isoformat()
        
        disconnected_clients = []
        for client_id, connection in self.sse_connections.items():
            success = await connection.send_message(message)
            if not success:
                disconnected_clients.append(client_id)
        
        # 清理断开的客户端
        for client_id in disconnected_clients:
            if client_id in self.sse_connections:
                del self.sse_connections[client_id]
    
    async def mcp_handler(self, request):
        """MCP 协议处理器"""
        try:
            data = await request.json()
            method = data.get('method')
            params = data.get('params', {})
            request_id = data.get('id')
            
            logger.info(f"📨 收到 MCP 请求: {method}")
            
            # 广播 MCP 请求事件
            await self.broadcast_to_sse_clients({
                "type": "mcp_request",
                "method": method,
                "request_id": request_id
            })
            
            # 处理不同的 MCP 方法
            if method == 'initialize':
                result = await self.handle_initialize(params)
            elif method == 'tools/list':
                result = await self.handle_list_tools(params)
            elif method == 'tools/call':
                result = await self.handle_call_tool(params)
            elif method == 'resources/list':
                result = await self.handle_list_resources(params)
            elif method == 'resources/read':
                result = await self.handle_read_resource(params)
            else:
                result = {
                    "error": {
                        "code": -32601,
                        "message": f"未知方法: {method}"
                    }
                }
            
            # 构造响应
            response_data = {
                "jsonrpc": "2.0",
                "id": request_id,
                **result
            }
            
            # 广播 MCP 响应事件
            await self.broadcast_to_sse_clients({
                "type": "mcp_response",
                "method": method,
                "request_id": request_id,
                "success": "result" in result
            })
            
            return web.json_response(response_data)
            
        except Exception as e:
            logger.error(f"❌ MCP 处理错误: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": data.get('id') if 'data' in locals() else None,
                "error": {
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            }
            return web.json_response(error_response, status=500)

    async def handle_initialize(self, params):
        """处理初始化请求"""
        return {
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {}
                },
                "serverInfo": {
                    "name": "graphiti-falkordb-gemini-sse",
                    "version": "1.0.0",
                    "description": "Graphiti knowledge graph with FalkorDB and Gemini via SSE"
                }
            }
        }

    async def handle_list_tools(self, params):
        """处理工具列表请求"""
        tools = [
            {
                "name": "add_episode",
                "description": "向知识图谱添加新的剧集内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "剧集名称"},
                        "content": {"type": "string", "description": "剧集内容"},
                        "episode_type": {
                            "type": "string",
                            "enum": ["text", "json"],
                            "description": "剧集类型",
                            "default": "text"
                        },
                        "description": {"type": "string", "description": "剧集描述（可选）"}
                    },
                    "required": ["name", "content"]
                }
            },
            {
                "name": "search_knowledge",
                "description": "在知识图谱中搜索相关信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索查询"},
                        "limit": {
                            "type": "integer",
                            "description": "结果数量限制",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 20
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "search_nodes",
                "description": "搜索知识图谱中的节点",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "节点搜索查询"},
                        "limit": {
                            "type": "integer",
                            "description": "结果数量限制",
                            "default": 10,
                            "minimum": 1,
                            "maximum": 50
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "get_graph_stats",
                "description": "获取知识图谱的统计信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_recent_episodes",
                "description": "获取最近添加的剧集",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "limit": {
                            "type": "integer",
                            "description": "返回的剧集数量",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 20
                        }
                    },
                    "required": []
                }
            }
        ]

        return {"result": {"tools": tools}}

    async def handle_call_tool(self, params):
        """处理工具调用请求"""
        tool_name = params.get('name')
        arguments = params.get('arguments', {})

        try:
            if tool_name == 'add_episode':
                result = await self.tool_add_episode(arguments)
            elif tool_name == 'search_knowledge':
                result = await self.tool_search_knowledge(arguments)
            elif tool_name == 'search_nodes':
                result = await self.tool_search_nodes(arguments)
            elif tool_name == 'get_graph_stats':
                result = await self.tool_get_graph_stats(arguments)
            elif tool_name == 'get_recent_episodes':
                result = await self.tool_get_recent_episodes(arguments)
            else:
                return {
                    "error": {
                        "code": -32602,
                        "message": f"未知工具: {tool_name}"
                    }
                }

            return {"result": {"content": [{"type": "text", "text": result}]}}

        except Exception as e:
            logger.error(f"❌ 工具调用失败 {tool_name}: {e}")
            return {
                "error": {
                    "code": -32603,
                    "message": f"工具执行错误: {str(e)}"
                }
            }

    async def handle_list_resources(self, params):
        """处理资源列表请求"""
        resources = [
            {
                "uri": "graphiti://stats",
                "name": "图谱统计",
                "description": "知识图谱的实时统计信息",
                "mimeType": "application/json"
            },
            {
                "uri": "graphiti://config",
                "name": "服务器配置",
                "description": "当前服务器配置信息",
                "mimeType": "application/json"
            }
        ]
        return {"result": {"resources": resources}}

    async def handle_read_resource(self, params):
        """处理资源读取请求"""
        uri = params.get('uri')

        if uri == "graphiti://stats":
            stats = await self.get_graph_statistics()
            return {
                "result": {
                    "contents": [{
                        "uri": uri,
                        "mimeType": "application/json",
                        "text": json.dumps(stats, indent=2, ensure_ascii=False)
                    }]
                }
            }
        elif uri == "graphiti://config":
            config = {
                "database": "FalkorDB",
                "llm_model": os.getenv('GEMINI_MODEL', 'gemini-2.5-flash'),
                "embedding_model": os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001'),
                "host": os.getenv('FALKORDB_HOST', 'localhost'),
                "port": os.getenv('FALKORDB_PORT', '6379'),
                "connected_clients": len(self.sse_connections)
            }
            return {
                "result": {
                    "contents": [{
                        "uri": uri,
                        "mimeType": "application/json",
                        "text": json.dumps(config, indent=2, ensure_ascii=False)
                    }]
                }
            }
        else:
            return {
                "error": {
                    "code": -32602,
                    "message": f"未知资源: {uri}"
                }
            }

    async def tool_add_episode(self, args):
        """添加剧集工具"""
        name = args["name"]
        content = args["content"]
        episode_type = args.get("episode_type", "text")
        description = args.get("description", "")

        # 确定剧集类型
        if episode_type == "json":
            ep_type = EpisodeType.json
            try:
                json.loads(content)
            except json.JSONDecodeError:
                raise ValueError("无效的JSON格式")
        else:
            ep_type = EpisodeType.text

        # 添加剧集
        result = await self.graphiti.add_episode(
            name=name,
            episode_body=content,
            source=ep_type,
            source_description=description,
            reference_time=datetime.now(timezone.utc)
        )

        response = f"✅ 成功添加剧集: {name}\n"
        response += f"📊 提取的节点数: {len(result.nodes)}\n"
        response += f"🔗 提取的边数: {len(result.edges)}\n"
        response += f"🏘️ 社区数: {len(result.communities)}\n"
        response += f"📅 添加时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # 广播事件到 SSE 客户端
        await self.broadcast_to_sse_clients({
            "type": "episode_added",
            "data": {
                "name": name,
                "episode_type": episode_type,
                "nodes_count": len(result.nodes),
                "edges_count": len(result.edges),
                "communities_count": len(result.communities)
            }
        })

        return response

    async def tool_search_knowledge(self, args):
        """搜索知识工具"""
        query = args["query"]
        limit = args.get("limit", 5)

        results = await self.graphiti.search(query=query, limit=limit)

        if not results:
            response = f"🔍 未找到与 '{query}' 相关的结果"
        else:
            response = f"🔍 搜索结果 ('{query}')，共找到 {len(results)} 条:\n\n"
            for i, result in enumerate(results, 1):
                response += f"{i}. {result.fact}\n"
                if hasattr(result, 'valid_at') and result.valid_at:
                    response += f"   📅 有效期: {result.valid_at}\n"
                response += f"   🆔 UUID: {result.uuid}\n\n"

        # 广播搜索事件
        await self.broadcast_to_sse_clients({
            "type": "knowledge_search",
            "data": {
                "query": query,
                "results_count": len(results),
                "limit": limit
            }
        })

        return response

    async def tool_search_nodes(self, args):
        """搜索节点工具"""
        query = args["query"]
        limit = args.get("limit", 10)

        try:
            # 使用节点混合搜索
            results = await self.graphiti.search(
                query=query,
                config=NODE_HYBRID_SEARCH_RRF,
                limit=limit
            )

            if not results:
                response = f"🔍 未找到与 '{query}' 相关的节点"
            else:
                response = f"🔍 节点搜索结果 ('{query}')，共找到 {len(results)} 个节点:\n\n"
                for i, result in enumerate(results, 1):
                    response += f"{i}. {result.name if hasattr(result, 'name') else result.fact}\n"
                    if hasattr(result, 'node_type'):
                        response += f"   🏷️ 类型: {result.node_type}\n"
                    if hasattr(result, 'created_at'):
                        response += f"   📅 创建时间: {result.created_at}\n"
                    response += f"   🆔 UUID: {result.uuid}\n\n"

            # 广播节点搜索事件
            await self.broadcast_to_sse_clients({
                "type": "node_search",
                "data": {
                    "query": query,
                    "results_count": len(results),
                    "limit": limit
                }
            })

            return response

        except Exception as e:
            logger.error(f"节点搜索失败: {e}")
            return f"❌ 节点搜索失败: {str(e)}"

    async def tool_get_graph_stats(self, args):
        """获取图统计信息工具"""
        try:
            stats = await self.get_graph_statistics()

            response = "📈 知识图谱统计信息:\n\n"
            response += f"🗄️ 数据库: FalkorDB\n"
            response += f"🤖 LLM: Gemini ({stats.get('llm_model', 'N/A')})\n"
            response += f"🔤 嵌入模型: {stats.get('embedding_model', 'N/A')}\n"
            response += f"📊 总节点数: {stats.get('total_nodes', 0)}\n"
            response += f"🔗 总边数: {stats.get('total_edges', 0)}\n"
            response += f"📚 剧集数: {stats.get('total_episodes', 0)}\n"
            response += f"🏘️ 社区数: {stats.get('total_communities', 0)}\n"
            response += f"📡 连接的客户端: {stats.get('connected_clients', 0)}\n"
            response += f"⏰ 统计时间: {stats.get('timestamp', 'N/A')}"

            return response

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return f"❌ 获取统计信息时出错: {str(e)}"

    async def tool_get_recent_episodes(self, args):
        """获取最近剧集工具"""
        limit = args.get("limit", 5)

        try:
            # 查询最近的剧集
            query = f"""
            MATCH (e:Episode)
            RETURN e.name as name, e.created_at as created_at, e.source_description as description
            ORDER BY e.created_at DESC
            LIMIT {limit}
            """

            results = await self.graphiti.driver.execute_query(query)

            if not results or not results[0]:
                return "📚 暂无剧集记录"

            response = f"📚 最近 {len(results[0])} 个剧集:\n\n"
            for i, record in enumerate(results[0], 1):
                episode = record[0]
                name = episode.get('name', 'N/A')
                created_at = episode.get('created_at', 'N/A')
                description = episode.get('description', '')

                response += f"{i}. {name}\n"
                response += f"   📅 创建时间: {created_at}\n"
                if description:
                    response += f"   📝 描述: {description}\n"
                response += "\n"

            return response

        except Exception as e:
            logger.error(f"获取最近剧集失败: {e}")
            return f"❌ 获取最近剧集时出错: {str(e)}"

    async def get_graph_statistics(self):
        """获取图统计信息"""
        try:
            stats = {}

            # 基本统计
            queries = {
                'total_nodes': "MATCH (n) RETURN count(n) as count",
                'total_edges': "MATCH ()-[r]->() RETURN count(r) as count",
                'total_episodes': "MATCH (e:Episode) RETURN count(e) as count",
                'total_communities': "MATCH (c:Community) RETURN count(c) as count"
            }

            for key, query in queries.items():
                try:
                    result = await self.graphiti.driver.execute_query(query)
                    if result and result[0]:
                        stats[key] = result[0][0].get('count', 0)
                    else:
                        stats[key] = 0
                except Exception as e:
                    logger.warning(f"统计查询失败 {key}: {e}")
                    stats[key] = 0

            # 添加配置信息
            stats.update({
                'llm_model': os.getenv('GEMINI_MODEL', 'gemini-2.5-flash'),
                'embedding_model': os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001'),
                'database_host': os.getenv('FALKORDB_HOST', 'localhost'),
                'database_port': os.getenv('FALKORDB_PORT', '6379'),
                'connected_clients': len(self.sse_connections),
                'timestamp': datetime.now().isoformat()
            })

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def test_add_episode(self, request):
        """测试添加剧集端点"""
        try:
            data = await request.json()
            result = await self.tool_add_episode(data)
            return web.json_response({"success": True, "result": result})
        except Exception as e:
            return web.json_response({"success": False, "error": str(e)}, status=400)

    async def test_search(self, request):
        """测试搜索端点"""
        try:
            data = await request.json()
            result = await self.tool_search_knowledge(data)
            return web.json_response({"success": True, "result": result})
        except Exception as e:
            return web.json_response({"success": False, "error": str(e)}, status=400)

    async def list_clients(self, request):
        """列出连接的客户端"""
        clients = []
        for client_id, connection in self.sse_connections.items():
            clients.append({
                "client_id": client_id,
                "connected": connection.connected,
                "last_heartbeat": connection.last_heartbeat.isoformat()
            })

        return web.json_response({
            "total_clients": len(clients),
            "clients": clients
        })

    async def index(self, request):
        """主页 Web 界面"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti SSE MCP 服务器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .panel {
            background: white;
            border: 1px solid #ddd;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #007bff;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        #messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .message {
            margin: 8px 0;
            padding: 8px;
            border-left: 3px solid #007bff;
            background: white;
            border-radius: 4px;
        }
        .message.heartbeat { border-left-color: #28a745; }
        .message.episode_added { border-left-color: #ffc107; }
        .message.search { border-left-color: #17a2b8; }
        .message.error { border-left-color: #dc3545; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Graphiti SSE MCP 服务器</h1>
        <p>FalkorDB + Gemini + SSE 协议 | 端口 8089</p>
    </div>

    <div class="panel">
        <h2>📊 服务器状态</h2>
        <div id="server-status" class="status">
            检查中...
        </div>
        <div class="stats-grid" id="stats-grid">
            <!-- 统计信息将在这里显示 -->
        </div>
        <button class="button" onclick="refreshStatus()">刷新状态</button>
        <button class="button" onclick="getClients()">查看客户端</button>
    </div>

    <div class="panel">
        <h2>🔌 API 端点</h2>
        <div class="endpoint">GET /sse - SSE 连接端点 (实时通信)</div>
        <div class="endpoint">POST /mcp - MCP 协议端点 (工具调用)</div>
        <div class="endpoint">GET /status - 状态检查</div>
        <div class="endpoint">GET /health - 健康检查</div>
        <div class="endpoint">GET /clients - 客户端列表</div>
        <div class="endpoint">POST /test/add_episode - 测试添加剧集</div>
        <div class="endpoint">POST /test/search - 测试搜索</div>
    </div>

    <div class="panel">
        <h2>🧪 功能测试</h2>

        <div class="test-form">
            <h3>添加剧集测试</h3>
            <div class="form-group">
                <label>剧集名称:</label>
                <input type="text" id="episode-name" placeholder="输入剧集名称">
            </div>
            <div class="form-group">
                <label>剧集内容:</label>
                <textarea id="episode-content" placeholder="输入剧集内容"></textarea>
            </div>
            <div class="form-group">
                <label>剧集类型:</label>
                <select id="episode-type">
                    <option value="text">文本</option>
                    <option value="json">JSON</option>
                </select>
            </div>
            <div class="form-group">
                <label>描述 (可选):</label>
                <input type="text" id="episode-description" placeholder="输入描述">
            </div>
            <button class="button" onclick="testAddEpisode()">添加剧集</button>
        </div>

        <div class="test-form">
            <h3>搜索测试</h3>
            <div class="form-group">
                <label>搜索查询:</label>
                <input type="text" id="search-query" placeholder="输入搜索内容">
            </div>
            <div class="form-group">
                <label>结果数量:</label>
                <input type="number" id="search-limit" value="5" min="1" max="20">
            </div>
            <button class="button" onclick="testSearch()">搜索知识</button>
        </div>

        <div id="test-results" style="margin-top: 20px;"></div>
    </div>

    <div class="panel">
        <h2>📡 实时消息 (SSE)</h2>
        <div style="margin-bottom: 15px;">
            <span id="connection-status">🔴 未连接</span>
            <button class="button" onclick="connectSSE()" id="connect-btn">连接 SSE</button>
            <button class="button danger" onclick="disconnectSSE()" id="disconnect-btn" disabled>断开连接</button>
            <button class="button" onclick="clearMessages()">清空消息</button>
        </div>
        <div id="messages"></div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;

        // SSE 连接管理
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource('/sse');

            eventSource.onopen = function(event) {
                isConnected = true;
                updateConnectionStatus('🟢 已连接', 'connected');
                document.getElementById('connect-btn').disabled = true;
                document.getElementById('disconnect-btn').disabled = false;
                addMessage('connection', '✅ SSE 连接已建立');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(data.type, data);
                } catch (e) {
                    addMessage('error', { message: 'JSON 解析错误: ' + event.data });
                }
            };

            eventSource.onerror = function(event) {
                isConnected = false;
                updateConnectionStatus('🔴 连接错误', 'error');
                document.getElementById('connect-btn').disabled = false;
                document.getElementById('disconnect-btn').disabled = true;
                addMessage('error', { message: '❌ SSE 连接错误' });
            };
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            isConnected = false;
            updateConnectionStatus('🔴 已断开', 'disconnected');
            document.getElementById('connect-btn').disabled = false;
            document.getElementById('disconnect-btn').disabled = true;
            addMessage('connection', '🔌 SSE 连接已断开');
        }

        function updateConnectionStatus(text, status) {
            const statusElement = document.getElementById('connection-status');
            statusElement.textContent = text;
            statusElement.className = status;
        }

        function addMessage(type, data) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            let content = `<strong>[${timestamp}] ${type.toUpperCase()}</strong><br>`;

            if (typeof data === 'string') {
                content += data;
            } else {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }

            messageDiv.innerHTML = content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            // 限制消息数量
            while (messagesDiv.children.length > 100) {
                messagesDiv.removeChild(messagesDiv.firstChild);
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 状态刷新
        async function refreshStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();

                const statusDiv = document.getElementById('server-status');
                statusDiv.innerHTML = `
                    ✅ 服务器运行正常<br>
                    🌐 端口: ${data.port}<br>
                    📡 协议: ${data.protocol}<br>
                    🗄️ 数据库: ${data.database}<br>
                    🤖 LLM: ${data.llm}<br>
                    👥 连接客户端: ${data.clients_connected}<br>
                    ⏰ 时间: ${new Date(data.timestamp).toLocaleString()}
                `;

                // 更新统计信息
                await updateStats();

            } catch (error) {
                document.getElementById('server-status').innerHTML = '❌ 无法连接到服务器';
            }
        }

        async function updateStats() {
            try {
                const response = await fetch('/mcp', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        id: 'stats-' + Date.now(),
                        method: 'tools/call',
                        params: {
                            name: 'get_graph_stats',
                            arguments: {}
                        }
                    })
                });

                const data = await response.json();
                if (data.result && data.result.content) {
                    const statsText = data.result.content[0].text;
                    // 这里可以解析统计信息并显示在网格中
                    // 简化处理，直接显示在状态区域
                }
            } catch (error) {
                console.error('获取统计信息失败:', error);
            }
        }

        async function getClients() {
            try {
                const response = await fetch('/clients');
                const data = await response.json();

                let clientsInfo = `总客户端数: ${data.total_clients}\\n\\n`;
                data.clients.forEach((client, index) => {
                    clientsInfo += `${index + 1}. ID: ${client.client_id}\\n`;
                    clientsInfo += `   状态: ${client.connected ? '已连接' : '已断开'}\\n`;
                    clientsInfo += `   最后心跳: ${new Date(client.last_heartbeat).toLocaleString()}\\n\\n`;
                });

                alert(clientsInfo);
            } catch (error) {
                alert('获取客户端信息失败: ' + error.message);
            }
        }

        // 测试功能
        async function testAddEpisode() {
            const name = document.getElementById('episode-name').value;
            const content = document.getElementById('episode-content').value;
            const episodeType = document.getElementById('episode-type').value;
            const description = document.getElementById('episode-description').value;

            if (!name || !content) {
                alert('请填写剧集名称和内容');
                return;
            }

            try {
                const response = await fetch('/test/add_episode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: name,
                        content: content,
                        episode_type: episodeType,
                        description: description
                    })
                });

                const data = await response.json();
                const resultDiv = document.getElementById('test-results');

                if (data.success) {
                    resultDiv.innerHTML = `<div class="status">✅ 添加成功<br><pre>${data.result}</pre></div>`;
                    // 清空表单
                    document.getElementById('episode-name').value = '';
                    document.getElementById('episode-content').value = '';
                    document.getElementById('episode-description').value = '';
                } else {
                    resultDiv.innerHTML = `<div class="status" style="background: #f8d7da; color: #721c24;">❌ 添加失败: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('test-results').innerHTML = `<div class="status" style="background: #f8d7da; color: #721c24;">❌ 请求失败: ${error.message}</div>`;
            }
        }

        async function testSearch() {
            const query = document.getElementById('search-query').value;
            const limit = parseInt(document.getElementById('search-limit').value);

            if (!query) {
                alert('请输入搜索查询');
                return;
            }

            try {
                const response = await fetch('/test/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: query,
                        limit: limit
                    })
                });

                const data = await response.json();
                const resultDiv = document.getElementById('test-results');

                if (data.success) {
                    resultDiv.innerHTML = `<div class="status">✅ 搜索完成<br><pre>${data.result}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="status" style="background: #f8d7da; color: #721c24;">❌ 搜索失败: ${data.error}</div>`;
                }
            } catch (error) {
                document.getElementById('test-results').innerHTML = `<div class="status" style="background: #f8d7da; color: #721c24;">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动执行
        window.onload = function() {
            refreshStatus();
            // 自动连接 SSE
            setTimeout(connectSSE, 1000);
        };

        // 页面卸载时断开连接
        window.onbeforeunload = function() {
            if (eventSource) {
                eventSource.close();
            }
        };
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')

    async def status(self, request):
        """状态检查"""
        return web.json_response({
            'status': 'running',
            'port': 8089,
            'protocol': 'SSE + MCP',
            'database': 'FalkorDB',
            'llm': 'Gemini',
            'clients_connected': len(self.sse_connections),
            'timestamp': datetime.now().isoformat()
        })

    async def health_check(self, request):
        """健康检查"""
        healthy = self.graphiti is not None
        return web.json_response({
            'healthy': healthy,
            'graphiti_initialized': healthy,
            'server_version': '1.0.0',
            'timestamp': datetime.now().isoformat()
        }, status=200 if healthy else 503)

async def main():
    """主函数"""
    logger.info("🚀 启动 Graphiti SSE MCP 服务器 (端口 8089)")

    # 检查环境变量
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        logger.warning("⚠️ 未设置 GOOGLE_API_KEY 环境变量")
        logger.info("请设置环境变量或使用 .env 文件")

    # 创建服务器实例
    server = GraphitiSSEMCPServer()

    try:
        # 初始化服务器
        if not await server.initialize():
            logger.error("❌ 服务器初始化失败，退出")
            return 1

        # 启动 Web 服务器
        runner = web.AppRunner(server.app)
        await runner.setup()

        site = web.TCPSite(runner, 'localhost', 8089)
        await site.start()

        logger.info("✅ Graphiti SSE MCP 服务器已启动")
        logger.info("🌐 Web 界面: http://localhost:8089")
        logger.info("📡 SSE 端点: http://localhost:8089/sse")
        logger.info("🔌 MCP 端点: http://localhost:8089/mcp")
        logger.info("📊 状态检查: http://localhost:8089/status")
        logger.info("🏥 健康检查: http://localhost:8089/health")
        logger.info("")
        logger.info("🔧 支持的 MCP 工具:")
        logger.info("  - add_episode: 添加剧集到知识图谱")
        logger.info("  - search_knowledge: 搜索知识图谱内容")
        logger.info("  - search_nodes: 搜索图谱节点")
        logger.info("  - get_graph_stats: 获取图谱统计信息")
        logger.info("  - get_recent_episodes: 获取最近的剧集")
        logger.info("")
        logger.info("📋 使用 Ctrl+C 停止服务器")

        # 等待关闭信号
        try:
            await server.shutdown_event.wait()
        except KeyboardInterrupt:
            logger.info("🛑 收到键盘中断信号")

        logger.info("🔄 正在关闭服务器...")

    except Exception as e:
        logger.error(f"❌ 服务器运行错误: {e}")
        return 1
    finally:
        # 清理资源
        await server.cleanup()
        logger.info("✅ 服务器已安全关闭")

    return 0

def run_server():
    """运行服务器的同步入口点"""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在退出...")
        return 0
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(run_server())
