#!/usr/bin/env python3
"""
Graphiti MCP Server with FalkorDB + Gemini
基于原始 MCP 服务器，配置为使用 FalkorDB 和 Gemini
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from falkordb_gemini_config import get_configured_graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF

# MCP 相关导入
try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
    )
except ImportError:
    print("错误: 需要安装 mcp 包")
    print("运行: pip install mcp")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局 Graphiti 实例
graphiti_instance = None

class GraphitiMCPServer:
    """Graphiti MCP 服务器类"""
    
    def __init__(self):
        self.server = Server("graphiti-falkordb-gemini")
        self.graphiti = None
        self._setup_handlers()
    
    async def initialize(self):
        """初始化 Graphiti 实例"""
        try:
            logger.info("初始化 Graphiti (FalkorDB + Gemini)...")
            self.graphiti = get_configured_graphiti()
            await self.graphiti.build_indices_and_constraints()
            logger.info("Graphiti 初始化完成")
        except Exception as e:
            logger.error(f"Graphiti 初始化失败: {e}")
            raise
    
    def _setup_handlers(self):
        """设置 MCP 处理器"""
        
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            """列出可用工具"""
            return [
                Tool(
                    name="add_episode",
                    description="向知识图谱添加新的剧集（文本或JSON）",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "description": "剧集名称"},
                            "content": {"type": "string", "description": "剧集内容（文本或JSON字符串）"},
                            "episode_type": {
                                "type": "string", 
                                "enum": ["text", "json"],
                                "description": "剧集类型"
                            },
                            "description": {"type": "string", "description": "剧集描述"}
                        },
                        "required": ["name", "content", "episode_type"]
                    }
                ),
                Tool(
                    name="search_knowledge",
                    description="在知识图谱中搜索信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "搜索查询"},
                            "limit": {"type": "integer", "description": "结果数量限制", "default": 5},
                            "center_node_uuid": {"type": "string", "description": "中心节点UUID（可选）"}
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="search_nodes",
                    description="搜索知识图谱中的节点",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "节点搜索查询"},
                            "limit": {"type": "integer", "description": "结果数量限制", "default": 5}
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="get_graph_stats",
                    description="获取知识图谱统计信息",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """调用工具"""
            try:
                if name == "add_episode":
                    return await self._add_episode(arguments)
                elif name == "search_knowledge":
                    return await self._search_knowledge(arguments)
                elif name == "search_nodes":
                    return await self._search_nodes(arguments)
                elif name == "get_graph_stats":
                    return await self._get_graph_stats(arguments)
                else:
                    return [TextContent(type="text", text=f"未知工具: {name}")]
            except Exception as e:
                logger.error(f"工具调用失败 {name}: {e}")
                return [TextContent(type="text", text=f"错误: {str(e)}")]
    
    async def _add_episode(self, args: Dict[str, Any]) -> List[TextContent]:
        """添加剧集"""
        name = args["name"]
        content = args["content"]
        episode_type = args["episode_type"]
        description = args.get("description", "")
        
        # 确定剧集类型
        if episode_type == "json":
            ep_type = EpisodeType.json
            # 验证 JSON 格式
            try:
                json.loads(content)
            except json.JSONDecodeError:
                return [TextContent(type="text", text="错误: 无效的JSON格式")]
        else:
            ep_type = EpisodeType.text
        
        # 添加剧集
        result = await self.graphiti.add_episode(
            name=name,
            episode_body=content,
            source=ep_type,
            source_description=description,
            reference_time=datetime.now(timezone.utc)
        )
        
        response = f"✅ 成功添加剧集: {name}\n"
        response += f"📊 提取的节点数: {len(result.nodes)}\n"
        response += f"🔗 提取的边数: {len(result.edges)}\n"
        response += f"🏘️ 社区数: {len(result.communities)}\n"
        
        if result.nodes:
            response += "\n📝 提取的主要节点:\n"
            for node in result.nodes[:3]:  # 显示前3个节点
                response += f"  • {node.name}: {node.summary[:100]}...\n"
        
        return [TextContent(type="text", text=response)]
    
    async def _search_knowledge(self, args: Dict[str, Any]) -> List[TextContent]:
        """搜索知识"""
        query = args["query"]
        limit = args.get("limit", 5)
        center_node_uuid = args.get("center_node_uuid")
        
        # 执行搜索
        results = await self.graphiti.search(
            query=query,
            limit=limit,
            center_node_uuid=center_node_uuid
        )
        
        if not results:
            return [TextContent(type="text", text=f"未找到与 '{query}' 相关的结果")]
        
        response = f"🔍 搜索结果 ('{query}'):\n\n"
        
        for i, result in enumerate(results, 1):
            response += f"{i}. **{result.fact}**\n"
            if hasattr(result, 'valid_at') and result.valid_at:
                response += f"   📅 有效期: {result.valid_at}\n"
            if hasattr(result, 'invalid_at') and result.invalid_at:
                response += f"   ⏰ 失效时间: {result.invalid_at}\n"
            response += f"   🆔 UUID: {result.uuid}\n\n"
        
        return [TextContent(type="text", text=response)]
    
    async def _search_nodes(self, args: Dict[str, Any]) -> List[TextContent]:
        """搜索节点"""
        query = args["query"]
        limit = args.get("limit", 5)
        
        # 使用节点搜索配置
        config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
        config.limit = limit
        
        results = await self.graphiti._search(query=query, config=config)
        
        if not results.nodes:
            return [TextContent(type="text", text=f"未找到与 '{query}' 相关的节点")]
        
        response = f"📊 节点搜索结果 ('{query}'):\n\n"
        
        for i, node in enumerate(results.nodes, 1):
            summary = node.summary[:150] + "..." if len(node.summary) > 150 else node.summary
            response += f"{i}. **{node.name}**\n"
            response += f"   📝 摘要: {summary}\n"
            response += f"   🏷️ 标签: {', '.join(node.labels)}\n"
            response += f"   📅 创建时间: {node.created_at}\n"
            response += f"   🆔 UUID: {node.uuid}\n\n"
        
        return [TextContent(type="text", text=response)]
    
    async def _get_graph_stats(self, args: Dict[str, Any]) -> List[TextContent]:
        """获取图统计信息"""
        try:
            # 执行统计查询
            stats_query = """
            MATCH (n) 
            RETURN 
                count(n) as total_nodes,
                count(DISTINCT labels(n)) as node_types
            """
            
            stats_result = await self.graphiti.driver.execute_query(stats_query)
            
            edges_query = "MATCH ()-[r]->() RETURN count(r) as total_edges"
            edges_result = await self.graphiti.driver.execute_query(edges_query)
            
            response = "📈 知识图谱统计信息:\n\n"
            
            if stats_result and stats_result[0]:
                stats = stats_result[0][0]
                response += f"📊 总节点数: {stats.get('total_nodes', 0)}\n"
                response += f"🏷️ 节点类型数: {stats.get('node_types', 0)}\n"
            
            if edges_result and edges_result[0]:
                edges = edges_result[0][0]
                response += f"🔗 总边数: {edges.get('total_edges', 0)}\n"
            
            response += f"\n🗄️ 数据库: FalkorDB\n"
            response += f"🤖 LLM: Gemini ({os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')})\n"
            response += f"🔤 嵌入模型: {os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001')}\n"
            
            return [TextContent(type="text", text=response)]
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return [TextContent(type="text", text=f"获取统计信息时出错: {str(e)}")]

async def main():
    """主函数"""
    logger.info("启动 Graphiti MCP Server (FalkorDB + Gemini)")
    
    # 创建服务器实例
    mcp_server = GraphitiMCPServer()
    
    try:
        # 初始化 Graphiti
        await mcp_server.initialize()
        
        # 启动 MCP 服务器
        async with stdio_server() as (read_stream, write_stream):
            await mcp_server.server.run(
                read_stream,
                write_stream,
                mcp_server.server.create_initialization_options()
            )
    
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
        raise
    finally:
        if mcp_server.graphiti:
            await mcp_server.graphiti.close()
        logger.info("MCP 服务器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
