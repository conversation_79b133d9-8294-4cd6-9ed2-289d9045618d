#!/usr/bin/env python3
"""
Graphiti SSE MCP 服务器
在端口 8089 上运行，支持 SSE 协议，供 MCP 客户端访问
使用 FalkorDB + Gemini 配置
"""

import asyncio
import json
import logging
import os
import sys
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from aiohttp import web, web_request
    from aiohttp.web import middleware
except ImportError:
    print("❌ 需要安装 aiohttp")
    print("运行: uv add aiohttp")
    sys.exit(1)

try:
    from falkordb_gemini_config import get_configured_graphiti
    from graphiti_core.nodes import EpisodeType
    from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
except ImportError as e:
    print(f"❌ 无法导入 Graphiti 模块: {e}")
    print("请确保已安装 graphiti-core 和相关依赖")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GraphitiSSEServer:
    """Graphiti SSE MCP 服务器"""
    
    def __init__(self):
        self.app = web.Application(middlewares=[self.cors_middleware])
        self.graphiti = None
        self.clients = {}  # 存储 SSE 客户端连接
        self.setup_routes()
    
    @middleware
    async def cors_middleware(self, request, handler):
        """CORS 中间件"""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Cache-Control'
        return response
    
    def setup_routes(self):
        """设置路由"""
        # SSE 端点
        self.app.router.add_get('/sse', self.sse_handler)
        
        # MCP 协议端点
        self.app.router.add_post('/mcp', self.mcp_handler)
        
        # 状态和管理端点
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_get('/health', self.health_check)
        
        # OPTIONS 处理
        self.app.router.add_options('/{path:.*}', self.options_handler)
    
    async def options_handler(self, request):
        """处理 OPTIONS 请求"""
        return web.Response(status=200)
    
    async def initialize(self):
        """初始化 Graphiti"""
        try:
            logger.info("🚀 初始化 Graphiti (FalkorDB + Gemini)...")
            self.graphiti = get_configured_graphiti()
            await self.graphiti.build_indices_and_constraints()
            logger.info("✅ Graphiti 初始化完成")
            return True
        except Exception as e:
            logger.error(f"❌ Graphiti 初始化失败: {e}")
            return False
    
    async def sse_handler(self, request):
        """SSE 连接处理器"""
        response = web.StreamResponse()
        response.headers['Content-Type'] = 'text/event-stream'
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Connection'] = 'keep-alive'
        
        await response.prepare(request)
        
        # 生成客户端 ID
        client_id = str(uuid.uuid4())
        self.clients[client_id] = response
        
        logger.info(f"📡 新的 SSE 客户端连接: {client_id}")
        
        try:
            # 发送欢迎消息
            await self.send_sse_message(response, {
                "type": "connection",
                "data": {
                    "client_id": client_id,
                    "message": "连接到 Graphiti SSE 服务器",
                    "server_info": {
                        "database": "FalkorDB",
                        "llm": "Gemini",
                        "port": 8089
                    }
                }
            })
            
            # 保持连接
            while True:
                await asyncio.sleep(30)  # 每30秒发送心跳
                await self.send_sse_message(response, {
                    "type": "heartbeat",
                    "data": {"timestamp": datetime.now().isoformat()}
                })
                
        except asyncio.CancelledError:
            logger.info(f"📡 SSE 客户端断开连接: {client_id}")
        except Exception as e:
            logger.error(f"❌ SSE 连接错误: {e}")
        finally:
            if client_id in self.clients:
                del self.clients[client_id]
        
        return response
    
    async def send_sse_message(self, response, message):
        """发送 SSE 消息"""
        try:
            data = json.dumps(message, ensure_ascii=False)
            await response.write(f"data: {data}\n\n".encode('utf-8'))
        except Exception as e:
            logger.error(f"❌ 发送 SSE 消息失败: {e}")
    
    async def broadcast_to_clients(self, message):
        """向所有客户端广播消息"""
        if not self.clients:
            return
        
        disconnected_clients = []
        for client_id, response in self.clients.items():
            try:
                await self.send_sse_message(response, message)
            except Exception as e:
                logger.warning(f"⚠️ 向客户端 {client_id} 发送消息失败: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的客户端
        for client_id in disconnected_clients:
            if client_id in self.clients:
                del self.clients[client_id]
    
    async def mcp_handler(self, request):
        """MCP 协议处理器"""
        try:
            data = await request.json()
            method = data.get('method')
            params = data.get('params', {})
            request_id = data.get('id')
            
            logger.info(f"📨 收到 MCP 请求: {method}")
            
            # 处理不同的 MCP 方法
            if method == 'initialize':
                result = await self.handle_initialize(params)
            elif method == 'tools/list':
                result = await self.handle_list_tools(params)
            elif method == 'tools/call':
                result = await self.handle_call_tool(params)
            elif method == 'resources/list':
                result = await self.handle_list_resources(params)
            else:
                result = {
                    "error": {
                        "code": -32601,
                        "message": f"未知方法: {method}"
                    }
                }
            
            # 构造响应
            response_data = {
                "jsonrpc": "2.0",
                "id": request_id,
                **result
            }
            
            # 广播到 SSE 客户端
            await self.broadcast_to_clients({
                "type": "mcp_response",
                "data": {
                    "method": method,
                    "response": response_data
                }
            })
            
            return web.json_response(response_data)
            
        except Exception as e:
            logger.error(f"❌ MCP 处理错误: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": data.get('id') if 'data' in locals() else None,
                "error": {
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            }
            return web.json_response(error_response, status=500)
    
    async def handle_initialize(self, params):
        """处理初始化请求"""
        return {
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {},
                    "resources": {}
                },
                "serverInfo": {
                    "name": "graphiti-falkordb-gemini",
                    "version": "1.0.0"
                }
            }
        }
    
    async def handle_list_tools(self, params):
        """处理工具列表请求"""
        tools = [
            {
                "name": "add_episode",
                "description": "向知识图谱添加新的剧集",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "剧集名称"},
                        "content": {"type": "string", "description": "剧集内容"},
                        "episode_type": {"type": "string", "enum": ["text", "json"], "description": "剧集类型"},
                        "description": {"type": "string", "description": "剧集描述"}
                    },
                    "required": ["name", "content", "episode_type"]
                }
            },
            {
                "name": "search_knowledge",
                "description": "在知识图谱中搜索信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索查询"},
                        "limit": {"type": "integer", "description": "结果数量限制", "default": 5}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "get_graph_stats",
                "description": "获取知识图谱统计信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        ]
        
        return {"result": {"tools": tools}}
    
    async def handle_call_tool(self, params):
        """处理工具调用请求"""
        tool_name = params.get('name')
        arguments = params.get('arguments', {})
        
        try:
            if tool_name == 'add_episode':
                result = await self.tool_add_episode(arguments)
            elif tool_name == 'search_knowledge':
                result = await self.tool_search_knowledge(arguments)
            elif tool_name == 'get_graph_stats':
                result = await self.tool_get_graph_stats(arguments)
            else:
                return {
                    "error": {
                        "code": -32602,
                        "message": f"未知工具: {tool_name}"
                    }
                }
            
            return {"result": {"content": [{"type": "text", "text": result}]}}
            
        except Exception as e:
            logger.error(f"❌ 工具调用失败 {tool_name}: {e}")
            return {
                "error": {
                    "code": -32603,
                    "message": f"工具执行错误: {str(e)}"
                }
            }
    
    async def handle_list_resources(self, params):
        """处理资源列表请求"""
        return {"result": {"resources": []}}
    
    async def tool_add_episode(self, args):
        """添加剧集工具"""
        name = args["name"]
        content = args["content"]
        episode_type = args["episode_type"]
        description = args.get("description", "")
        
        # 确定剧集类型
        if episode_type == "json":
            ep_type = EpisodeType.json
            try:
                json.loads(content)
            except json.JSONDecodeError:
                raise ValueError("无效的JSON格式")
        else:
            ep_type = EpisodeType.text
        
        # 添加剧集
        result = await self.graphiti.add_episode(
            name=name,
            episode_body=content,
            source=ep_type,
            source_description=description,
            reference_time=datetime.now(timezone.utc)
        )
        
        response = f"✅ 成功添加剧集: {name}\n"
        response += f"📊 提取的节点数: {len(result.nodes)}\n"
        response += f"🔗 提取的边数: {len(result.edges)}\n"
        response += f"🏘️ 社区数: {len(result.communities)}"
        
        # 广播事件
        await self.broadcast_to_clients({
            "type": "episode_added",
            "data": {
                "name": name,
                "nodes_count": len(result.nodes),
                "edges_count": len(result.edges),
                "communities_count": len(result.communities)
            }
        })
        
        return response
    
    async def tool_search_knowledge(self, args):
        """搜索知识工具"""
        query = args["query"]
        limit = args.get("limit", 5)
        
        results = await self.graphiti.search(query=query, limit=limit)
        
        if not results:
            return f"未找到与 '{query}' 相关的结果"
        
        response = f"🔍 搜索结果 ('{query}'):\n\n"
        for i, result in enumerate(results, 1):
            response += f"{i}. {result.fact}\n"
            if hasattr(result, 'valid_at') and result.valid_at:
                response += f"   📅 有效期: {result.valid_at}\n"
            response += f"   🆔 UUID: {result.uuid}\n\n"
        
        # 广播搜索事件
        await self.broadcast_to_clients({
            "type": "search_performed",
            "data": {
                "query": query,
                "results_count": len(results)
            }
        })
        
        return response
    
    async def tool_get_graph_stats(self, args):
        """获取图统计信息工具"""
        try:
            stats_query = "MATCH (n) RETURN count(n) as total_nodes"
            stats_result = await self.graphiti.driver.execute_query(stats_query)
            
            edges_query = "MATCH ()-[r]->() RETURN count(r) as total_edges"
            edges_result = await self.graphiti.driver.execute_query(edges_query)
            
            response = "📈 知识图谱统计信息:\n\n"
            response += f"🗄️ 数据库: FalkorDB\n"
            response += f"🤖 LLM: Gemini ({os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')})\n"
            response += f"🔤 嵌入模型: {os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001')}\n"
            
            if stats_result and stats_result[0]:
                stats = stats_result[0][0]
                response += f"📊 总节点数: {stats.get('total_nodes', 0)}\n"
            
            if edges_result and edges_result[0]:
                edges = edges_result[0][0]
                response += f"🔗 总边数: {edges.get('total_edges', 0)}\n"
            
            return response
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return f"获取统计信息时出错: {str(e)}"
    
    async def index(self, request):
        """主页"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphiti SSE MCP 服务器</title>
    <style>
        body {{ font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }}
        .panel {{ border: 1px solid #ddd; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .status {{ padding: 10px; margin: 10px 0; border-radius: 4px; background: #d4edda; color: #155724; }}
        .endpoint {{ background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; font-family: monospace; }}
        #messages {{ height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa; }}
        .message {{ margin: 5px 0; padding: 5px; border-left: 3px solid #007bff; }}
    </style>
</head>
<body>
    <h1>🚀 Graphiti SSE MCP 服务器</h1>
    
    <div class="panel">
        <h2>📊 服务器状态</h2>
        <div class="status">
            ✅ 服务器运行中 - 端口 8089<br>
            🗄️ 数据库: FalkorDB<br>
            🤖 LLM: Gemini<br>
            📡 协议: SSE + MCP
        </div>
    </div>
    
    <div class="panel">
        <h2>🔌 API 端点</h2>
        <div class="endpoint">GET /sse - SSE 连接端点</div>
        <div class="endpoint">POST /mcp - MCP 协议端点</div>
        <div class="endpoint">GET /status - 状态检查</div>
        <div class="endpoint">GET /health - 健康检查</div>
    </div>
    
    <div class="panel">
        <h2>📡 实时消息 (SSE)</h2>
        <div id="messages"></div>
        <button onclick="clearMessages()">清空消息</button>
    </div>
    
    <script>
        const eventSource = new EventSource('/sse');
        const messagesDiv = document.getElementById('messages');
        
        eventSource.onmessage = function(event) {{
            const data = JSON.parse(event.data);
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <strong>${{data.type}}</strong> - ${{new Date().toLocaleTimeString()}}<br>
                <pre>${{JSON.stringify(data.data, null, 2)}}</pre>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }};
        
        eventSource.onerror = function(event) {{
            console.error('SSE 连接错误:', event);
        }};
        
        function clearMessages() {{
            messagesDiv.innerHTML = '';
        }}
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def status(self, request):
        """状态检查"""
        return web.json_response({
            'status': 'running',
            'port': 8089,
            'protocol': 'SSE + MCP',
            'database': 'FalkorDB',
            'llm': 'Gemini',
            'clients_connected': len(self.clients),
            'timestamp': datetime.now().isoformat()
        })
    
    async def health_check(self, request):
        """健康检查"""
        healthy = self.graphiti is not None
        return web.json_response({
            'healthy': healthy,
            'graphiti_initialized': healthy,
            'timestamp': datetime.now().isoformat()
        }, status=200 if healthy else 503)

async def main():
    """主函数"""
    logger.info("🚀 启动 Graphiti SSE MCP 服务器 (端口 8089)")
    
    # 创建服务器实例
    server = GraphitiSSEServer()
    
    try:
        # 初始化 Graphiti
        if not await server.initialize():
            logger.error("❌ 无法初始化 Graphiti，服务器将无法正常工作")
            return
        
        # 启动 Web 服务器
        runner = web.AppRunner(server.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', 8089)
        await site.start()
        
        logger.info("✅ SSE MCP 服务器已启动")
        logger.info("🌐 访问地址: http://localhost:8089")
        logger.info("📡 SSE 端点: http://localhost:8089/sse")
        logger.info("🔌 MCP 端点: http://localhost:8089/mcp")
        logger.info("📋 使用 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器错误: {e}")
    finally:
        if server.graphiti:
            await server.graphiti.close()
        logger.info("✅ 服务器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
