#!/usr/bin/env python3
"""
Graphiti SSE MCP 服务器启动脚本
自动检查环境、安装依赖并启动服务器
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    print(f"✅ Python 版本: {sys.version}")
    return True

def check_environment_variables():
    """检查环境变量"""
    print("\n🔍 检查环境变量...")
    
    required_vars = ['GOOGLE_API_KEY']
    optional_vars = {
        'GEMINI_MODEL': 'gemini-2.5-flash',
        'GEMINI_EMBEDDING_MODEL': 'text-embedding-001',
        'FALKORDB_HOST': 'localhost',
        'FALKORDB_PORT': '6379',
        'FALKORDB_DATABASE': 'graphiti_db',
        'LOG_LEVEL': 'INFO'
    }
    
    missing_required = []
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
        else:
            print(f"  ✅ {var}: 已设置")
    
    for var, default in optional_vars.items():
        value = os.getenv(var, default)
        print(f"  📋 {var}: {value}")
    
    if missing_required:
        print(f"\n⚠️ 缺少必需的环境变量: {', '.join(missing_required)}")
        print("请设置这些环境变量或创建 .env 文件")
        
        # 检查是否有 .env 文件
        env_file = Path(__file__).parent / '.env.falkordb_gemini'
        if env_file.exists():
            print(f"💡 发现配置模板: {env_file}")
            print("请复制为 .env 文件并填写 API 密钥")
        
        return False
    
    return True

def load_env_file():
    """加载环境变量文件"""
    env_files = ['.env', '.env.falkordb_gemini']
    
    for env_file in env_files:
        env_path = Path(__file__).parent / env_file
        if env_path.exists():
            print(f"📁 加载环境文件: {env_file}")
            try:
                from dotenv import load_dotenv
                load_dotenv(env_path)
                return True
            except ImportError:
                print("⚠️ 需要安装 python-dotenv")
                return False
    
    return True

def check_dependencies():
    """检查依赖项"""
    print("\n🔧 检查依赖项...")
    
    dependencies = [
        ('aiohttp', 'aiohttp'),
        ('graphiti_core', 'graphiti-core'),
        ('dotenv', 'python-dotenv')
    ]
    
    missing = []
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n📦 需要安装缺失的依赖: {', '.join(missing)}")
        print("运行以下命令安装:")
        print(f"  pip install {' '.join(missing)}")
        print("或者:")
        print(f"  uv add {' '.join(missing)}")
        return False
    
    return True

def check_config_files():
    """检查配置文件"""
    print("\n📁 检查配置文件...")
    
    required_files = [
        'falkordb_gemini_config.py',
        'graphiti_sse_mcp_server.py'
    ]
    
    missing = []
    for file in required_files:
        file_path = Path(__file__).parent / file
        if file_path.exists():
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing.append(file)
    
    if missing:
        print(f"\n❌ 缺少必需的配置文件: {', '.join(missing)}")
        return False
    
    return True

def check_falkordb_connection():
    """检查 FalkorDB 连接"""
    print("\n🗄️ 检查 FalkorDB 连接...")
    
    host = os.getenv('FALKORDB_HOST', 'localhost')
    port = int(os.getenv('FALKORDB_PORT', '6379'))
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"  ✅ FalkorDB 连接正常 ({host}:{port})")
            return True
        else:
            print(f"  ❌ 无法连接到 FalkorDB ({host}:{port})")
            print("  💡 请确保 FalkorDB 正在运行:")
            print("     docker run -p 6379:6379 -it --rm falkordb/falkordb:latest")
            return False
    except Exception as e:
        print(f"  ❌ 连接检查失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("\n🚀 启动 Graphiti SSE MCP 服务器...")
    
    server_script = Path(__file__).parent / 'graphiti_sse_mcp_server.py'
    
    try:
        # 使用当前 Python 解释器运行服务器
        subprocess.run([sys.executable, str(server_script)], check=True)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 服务器启动失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 启动错误: {e}")
        return False
    
    return True

def print_usage_info():
    """打印使用信息"""
    print("\n" + "="*60)
    print("📚 Graphiti SSE MCP 服务器使用指南")
    print("="*60)
    print("\n🌐 Web 界面:")
    print("  http://localhost:8089")
    print("\n📡 SSE 端点 (实时通信):")
    print("  http://localhost:8089/sse")
    print("\n🔌 MCP 端点 (工具调用):")
    print("  POST http://localhost:8089/mcp")
    print("\n📊 状态检查:")
    print("  http://localhost:8089/status")
    print("  http://localhost:8089/health")
    print("\n🧪 测试端点:")
    print("  POST http://localhost:8089/test/add_episode")
    print("  POST http://localhost:8089/test/search")
    print("\n🔧 支持的 MCP 工具:")
    print("  - add_episode: 添加剧集到知识图谱")
    print("  - search_knowledge: 搜索知识图谱内容")
    print("  - search_nodes: 搜索图谱节点")
    print("  - get_graph_stats: 获取图谱统计信息")
    print("  - get_recent_episodes: 获取最近的剧集")
    print("\n📋 MCP 客户端连接示例:")
    print("  1. 连接到 SSE 端点获取实时事件")
    print("  2. 向 MCP 端点发送 JSON-RPC 2.0 请求")
    print("  3. 使用 tools/call 方法调用具体工具")
    print("\n📖 详细文档: FalkorDB_Gemini_实施指南.md")
    print("="*60)

def main():
    """主函数"""
    print("🔍 Graphiti SSE MCP 服务器启动检查")
    print("="*50)
    
    # 检查 Python 版本
    if not check_python_version():
        return 1
    
    # 加载环境变量
    load_env_file()
    
    # 检查环境变量
    if not check_environment_variables():
        return 1
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 检查配置文件
    if not check_config_files():
        return 1
    
    # 检查 FalkorDB 连接
    if not check_falkordb_connection():
        print("\n⚠️ FalkorDB 连接失败，但服务器仍可启动")
        print("某些功能可能无法正常工作")
    
    print("\n✅ 所有检查通过，准备启动服务器...")
    
    # 打印使用信息
    print_usage_info()
    
    # 启动服务器
    return 0 if start_server() else 1

if __name__ == "__main__":
    sys.exit(main())
