# Graphiti SSE MCP 服务器环境变量配置
# 复制此文件为 .env 并填写实际值

# Google Gemini API 配置 (必需)
GOOGLE_API_KEY=your_google_api_key_here

# Gemini 模型配置 (可选)
GEMINI_MODEL=gemini-2.5-flash
GEMINI_SMALL_MODEL=gemini-2.5-flash-lite-preview-06-17
GEMINI_EMBEDDING_MODEL=text-embedding-001

# FalkorDB 数据库配置 (可选)
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_DATABASE=graphiti_db
FALKORDB_USERNAME=
FALKORDB_PASSWORD=

# 性能配置 (可选)
SEMAPHORE_LIMIT=5
MAX_COROUTINES=10
STORE_RAW_EPISODE_CONTENT=true

# 日志配置 (可选)
LOG_LEVEL=INFO

# 服务器配置 (可选)
SERVER_HOST=localhost
SERVER_PORT=8089
