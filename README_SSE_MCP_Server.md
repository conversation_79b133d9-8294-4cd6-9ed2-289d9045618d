# Graphiti SSE MCP 服务器

一个基于 Graphiti 知识图谱的 SSE (Server-Sent Events) 和 MCP (Model Context Protocol) 服务器，使用 FalkorDB 作为图数据库后端，Gemini 作为 LLM 模型。

## 🚀 功能特性

### 核心功能
- **知识图谱管理**: 基于 Graphiti 的时序知识图谱
- **实时通信**: 支持 SSE 协议的实时事件推送
- **MCP 协议**: 完整的 Model Context Protocol 支持
- **Web 界面**: 直观的管理和测试界面
- **多客户端**: 支持多个 MCP 客户端同时连接

### 技术栈
- **图数据库**: FalkorDB (Redis-based)
- **LLM 模型**: Google Gemini
- **Web 框架**: aiohttp (异步)
- **协议支持**: SSE, MCP, HTTP/JSON
- **前端**: HTML5 + JavaScript (原生)

## 📦 安装和配置

### 1. 环境要求
- Python 3.8+
- FalkorDB 服务器
- Google Gemini API 密钥

### 2. 安装依赖
```bash
# 使用 pip
pip install aiohttp graphiti-core[falkordb,google-genai] python-dotenv

# 或使用 uv
uv add aiohttp graphiti-core[falkordb,google-genai] python-dotenv
```

### 3. 环境配置
复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置必需的环境变量：
```env
# 必需
GOOGLE_API_KEY=your_google_api_key_here

# 可选 (有默认值)
GEMINI_MODEL=gemini-2.5-flash
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
```

### 4. 启动 FalkorDB
```bash
# 使用 Docker
docker run -p 6379:6379 -it --rm falkordb/falkordb:latest

# 或使用 Docker Compose
docker-compose up -d falkordb
```

## 🎯 快速开始

### 方法 1: 使用启动脚本 (推荐)
```bash
python start_graphiti_server.py
```

启动脚本会自动：
- 检查 Python 版本和依赖
- 验证环境变量配置
- 测试 FalkorDB 连接
- 启动服务器

### 方法 2: 直接启动
```bash
python graphiti_sse_mcp_server.py
```

### 访问服务
- **Web 界面**: http://localhost:8089
- **SSE 端点**: http://localhost:8089/sse
- **MCP 端点**: http://localhost:8089/mcp
- **状态检查**: http://localhost:8089/status

## 🔧 MCP 工具

服务器提供以下 MCP 工具：

### 1. add_episode
添加新剧集到知识图谱
```json
{
  "name": "add_episode",
  "arguments": {
    "name": "剧集名称",
    "content": "剧集内容",
    "episode_type": "text",
    "description": "可选描述"
  }
}
```

### 2. search_knowledge
搜索知识图谱内容
```json
{
  "name": "search_knowledge",
  "arguments": {
    "query": "搜索查询",
    "limit": 5
  }
}
```

### 3. search_nodes
搜索图谱节点
```json
{
  "name": "search_nodes",
  "arguments": {
    "query": "节点搜索查询",
    "limit": 10
  }
}
```

### 4. get_graph_stats
获取图谱统计信息
```json
{
  "name": "get_graph_stats",
  "arguments": {}
}
```

### 5. get_recent_episodes
获取最近的剧集
```json
{
  "name": "get_recent_episodes",
  "arguments": {
    "limit": 5
  }
}
```

## 📡 SSE 事件

服务器通过 SSE 推送以下事件：

- `connection_established`: 连接建立
- `heartbeat`: 心跳信号
- `episode_added`: 新剧集添加
- `knowledge_search`: 知识搜索执行
- `node_search`: 节点搜索执行
- `mcp_request`: MCP 请求接收
- `mcp_response`: MCP 响应发送

## 🧪 测试和示例

### 1. Web 界面测试
访问 http://localhost:8089 使用内置的测试界面

### 2. MCP 客户端示例
```bash
python mcp_client_example.py
```

### 3. 手动 API 测试
```bash
# 添加剧集
curl -X POST http://localhost:8089/test/add_episode \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试剧集",
    "content": "这是一个测试剧集的内容",
    "episode_type": "text"
  }'

# 搜索知识
curl -X POST http://localhost:8089/test/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "测试",
    "limit": 5
  }'
```

### 4. MCP 协议测试
```bash
# 初始化
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "1",
    "method": "initialize",
    "params": {}
  }'

# 列出工具
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "2",
    "method": "tools/list",
    "params": {}
  }'
```

## 🔍 监控和调试

### 日志级别
设置环境变量 `LOG_LEVEL` 控制日志详细程度：
```env
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR
```

### 状态端点
- `GET /status`: 服务器状态
- `GET /health`: 健康检查
- `GET /clients`: 连接的客户端列表

### 实时监控
通过 SSE 连接可以实时监控：
- 客户端连接状态
- MCP 请求/响应
- 知识图谱操作
- 系统事件

## 🛠️ 开发和扩展

### 添加新的 MCP 工具
1. 在 `handle_list_tools` 中添加工具定义
2. 在 `handle_call_tool` 中添加工具调用逻辑
3. 实现具体的工具方法

### 自定义 SSE 事件
1. 定义新的事件类型
2. 在适当的位置调用 `broadcast_to_sse_clients`
3. 在客户端处理新的事件类型

### 配置扩展
修改 `falkordb_gemini_config.py` 添加新的配置选项

## 📚 相关文档

- [Graphiti 官方文档](https://github.com/getzep/graphiti)
- [FalkorDB 文档](https://docs.falkordb.com/)
- [MCP 协议规范](https://modelcontextprotocol.io/)
- [SSE 规范](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
