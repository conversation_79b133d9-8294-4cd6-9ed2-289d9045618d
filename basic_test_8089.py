#!/usr/bin/env python3
"""
基础配置测试脚本
验证 FalkorDB + Gemini 配置是否正确设置
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

def check_environment():
    """检查环境变量配置"""
    print("🔍 检查环境变量配置...")
    
    env_vars = {
        'GOOGLE_API_KEY': os.getenv('GOOGLE_API_KEY'),
        'GEMINI_MODEL': os.getenv('GEMINI_MODEL', 'gemini-2.5-flash'),
        'GEMINI_SMALL_MODEL': os.getenv('GEMINI_SMALL_MODEL', 'gemini-2.5-flash-lite-preview-06-17'),
        'GEMINI_EMBEDDING_MODEL': os.getenv('GEMINI_EMBEDDING_MODEL', 'text-embedding-001'),
        'FALKORDB_HOST': os.getenv('FALKORDB_HOST', 'localhost'),
        'FALKORDB_PORT': os.getenv('FALKORDB_PORT', '6379'),
        'FALKORDB_DATABASE': os.getenv('FALKORDB_DATABASE', 'graphiti_db'),
        'SEMAPHORE_LIMIT': os.getenv('SEMAPHORE_LIMIT', '5'),
    }
    
    print("\n📋 环境变量状态:")
    for key, value in env_vars.items():
        if key == 'GOOGLE_API_KEY':
            status = "✅ 已设置" if value else "❌ 未设置"
            print(f"  {key}: {status}")
        else:
            print(f"  {key}: {value}")
    
    return env_vars

def check_files():
    """检查必要文件是否存在"""
    print("\n📁 检查配置文件...")
    
    required_files = [
        'falkordb_gemini_config.py',
        'falkordb_gemini_example.py',
        '.env.falkordb_gemini',
        'install_falkordb_gemini.py',
        'FalkorDB_Gemini_实施指南.md',
        'mcp_config_falkordb_gemini.json',
        'graphiti_mcp_server_falkordb_gemini.py'
    ]
    
    file_status = {}
    for file in required_files:
        file_path = Path(__file__).parent / file
        exists = file_path.exists()
        file_status[file] = exists
        status = "✅" if exists else "❌"
        print(f"  {status} {file}")
    
    return file_status

def test_imports():
    """测试必要模块导入"""
    print("\n🔧 测试模块导入...")
    
    import_tests = []
    
    # 测试基础模块
    try:
        import json
        import logging
        import asyncio
        from datetime import datetime, timezone
        print("  ✅ 基础 Python 模块")
        import_tests.append(("基础模块", True, ""))
    except Exception as e:
        print(f"  ❌ 基础 Python 模块: {e}")
        import_tests.append(("基础模块", False, str(e)))
    
    # 测试 dotenv
    try:
        from dotenv import load_dotenv
        print("  ✅ python-dotenv")
        import_tests.append(("python-dotenv", True, ""))
    except ImportError as e:
        print(f"  ❌ python-dotenv: {e}")
        import_tests.append(("python-dotenv", False, str(e)))
    
    # 测试 graphiti_core (如果可用)
    try:
        import graphiti_core
        print(f"  ✅ graphiti_core (版本: {getattr(graphiti_core, '__version__', '未知')})")
        import_tests.append(("graphiti_core", True, ""))
    except ImportError as e:
        print(f"  ⚠️  graphiti_core: {e}")
        import_tests.append(("graphiti_core", False, str(e)))
    
    # 测试 FalkorDB 驱动
    try:
        from graphiti_core.driver.falkordb_driver import FalkorDriver
        print("  ✅ FalkorDB 驱动")
        import_tests.append(("FalkorDB驱动", True, ""))
    except ImportError as e:
        print(f"  ⚠️  FalkorDB 驱动: {e}")
        import_tests.append(("FalkorDB驱动", False, str(e)))
    
    # 测试 Gemini 客户端
    try:
        from graphiti_core.llm_client.gemini_client import GeminiClient
        from graphiti_core.embedder.gemini import GeminiEmbedder
        print("  ✅ Gemini 客户端")
        import_tests.append(("Gemini客户端", True, ""))
    except ImportError as e:
        print(f"  ⚠️  Gemini 客户端: {e}")
        import_tests.append(("Gemini客户端", False, str(e)))
    
    return import_tests

def create_test_report():
    """创建测试报告"""
    print("\n📊 生成测试报告...")
    
    env_vars = check_environment()
    file_status = check_files()
    import_tests = test_imports()
    
    # 计算就绪状态
    api_key_set = bool(env_vars.get('GOOGLE_API_KEY'))
    all_files_exist = all(file_status.values())
    core_imports_ok = any(test[0] == "graphiti_core" and test[1] for test in import_tests)
    
    ready_for_testing = api_key_set and all_files_exist and core_imports_ok
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": {
            "environment_variables": env_vars,
            "file_status": file_status,
            "import_tests": [{"name": t[0], "success": t[1], "error": t[2]} for t in import_tests],
            "ready_for_testing": ready_for_testing
        },
        "summary": {
            "api_key_configured": api_key_set,
            "all_files_present": all_files_exist,
            "core_modules_available": core_imports_ok,
            "overall_status": "READY" if ready_for_testing else "NOT_READY"
        }
    }
    
    # 保存报告
    report_file = Path(__file__).parent / "test_report_8089.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 测试报告已保存: {report_file}")
    
    return report

def print_summary(report):
    """打印测试总结"""
    print("\n" + "="*60)
    print("🎯 FalkorDB + Gemini 配置测试总结")
    print("="*60)
    
    summary = report["summary"]
    
    print(f"📅 测试时间: {report['timestamp']}")
    print(f"🔑 API 密钥配置: {'✅ 已配置' if summary['api_key_configured'] else '❌ 未配置'}")
    print(f"📁 配置文件: {'✅ 完整' if summary['all_files_present'] else '❌ 缺失'}")
    print(f"🔧 核心模块: {'✅ 可用' if summary['core_modules_available'] else '❌ 不可用'}")
    
    status = summary['overall_status']
    if status == "READY":
        print("\n🎉 状态: ✅ 准备就绪")
        print("\n📋 下一步操作:")
        print("1. 启动 FalkorDB: docker run -p 6379:6379 -it --rm falkordb/falkordb:latest")
        print("2. 运行完整示例: python falkordb_gemini_example.py")
        print("3. 启动 MCP 服务器: python graphiti_mcp_server_falkordb_gemini.py")
    else:
        print("\n⚠️  状态: ❌ 未准备就绪")
        print("\n🔧 需要解决的问题:")
        if not summary['api_key_configured']:
            print("- 设置 GOOGLE_API_KEY 环境变量")
        if not summary['core_modules_available']:
            print("- 安装 graphiti-core: pip install 'graphiti-core[falkordb,google-genai]'")
    
    print("\n📚 参考文档: FalkorDB_Gemini_实施指南.md")
    print("="*60)

def simulate_port_8089():
    """模拟端口 8089 服务"""
    print(f"\n🌐 模拟端口 8089 服务状态:")
    print(f"   地址: http://localhost:8089")
    print(f"   状态: ✅ 配置测试完成")
    print(f"   时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    print("🚀 FalkorDB + Gemini 配置测试 (端口 8089)")
    print("="*60)
    
    try:
        # 运行所有测试
        report = create_test_report()
        
        # 打印总结
        print_summary(report)
        
        # 模拟端口服务
        simulate_port_8089()
        
        return report["summary"]["overall_status"] == "READY"
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
