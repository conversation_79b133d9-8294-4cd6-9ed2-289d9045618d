#!/usr/bin/env python3
"""
FalkorDB + Gemini 完整示例
演示如何使用 FalkorDB 作为图数据库后端，Gemini 作为 LLM 提供商
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from falkordb_gemini_config import get_configured_graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def main():
    """主函数 - 演示完整的 Graphiti 工作流程"""
    
    print("🚀 FalkorDB + Gemini 示例开始")
    print("=" * 60)
    
    # 1. 初始化 Graphiti
    print("\n📊 1. 初始化 Graphiti...")
    graphiti = get_configured_graphiti()
    
    try:
        # 2. 构建索引和约束
        print("\n🔧 2. 构建数据库索引和约束...")
        await graphiti.build_indices_and_constraints()
        print("✅ 索引和约束构建完成")
        
        # 3. 添加示例数据
        print("\n📝 3. 添加示例剧集...")
        await add_sample_episodes(graphiti)
        
        # 4. 执行搜索操作
        print("\n🔍 4. 执行搜索操作...")
        await perform_searches(graphiti)
        
        # 5. 演示高级功能
        print("\n⚡ 5. 演示高级功能...")
        await demonstrate_advanced_features(graphiti)
        
    finally:
        # 6. 清理资源
        print("\n🧹 6. 清理资源...")
        await graphiti.close()
        print("✅ 连接已关闭")
    
    print("\n🎉 示例完成！")


async def add_sample_episodes(graphiti):
    """添加示例剧集数据"""
    
    episodes = [
        {
            'name': '科技新闻 001',
            'content': 'OpenAI 发布了新的 GPT-4 模型，具有更强的推理能力。该模型在数学和科学问题上表现出色。',
            'type': EpisodeType.text,
            'description': '科技新闻摘要'
        },
        {
            'name': '科技新闻 002', 
            'content': 'Google 推出了 Gemini 2.5 模型，支持多模态输入和结构化输出。',
            'type': EpisodeType.text,
            'description': '科技新闻摘要'
        },
        {
            'name': '公司信息 001',
            'content': {
                'company': 'FalkorDB',
                'type': '图数据库',
                'features': ['Redis 兼容', 'Cypher 查询', '高性能'],
                'use_cases': ['知识图谱', '推荐系统', '欺诈检测']
            },
            'type': EpisodeType.json,
            'description': '公司产品信息'
        },
        {
            'name': '技术对比 001',
            'content': {
                'comparison': 'Neo4j vs FalkorDB',
                'neo4j': {
                    'pros': ['成熟生态', '丰富工具'],
                    'cons': ['资源消耗大', '许可成本']
                },
                'falkordb': {
                    'pros': ['高性能', 'Redis 兼容', '开源'],
                    'cons': ['生态较新', '工具较少']
                }
            },
            'type': EpisodeType.json,
            'description': '技术对比分析'
        }
    ]
    
    for i, episode in enumerate(episodes):
        content = episode['content']
        if isinstance(content, dict):
            content = json.dumps(content, ensure_ascii=False)
            
        result = await graphiti.add_episode(
            name=episode['name'],
            episode_body=content,
            source=episode['type'],
            source_description=episode['description'],
            reference_time=datetime.now(timezone.utc)
        )
        
        print(f"✅ 添加剧集: {episode['name']}")
        print(f"   - 提取的节点数: {len(result.nodes)}")
        print(f"   - 提取的边数: {len(result.edges)}")


async def perform_searches(graphiti):
    """执行各种搜索操作"""
    
    # 基础混合搜索
    print("\n🔍 基础混合搜索:")
    query = "什么是 Gemini 模型？"
    print(f"查询: {query}")
    
    results = await graphiti.search(query, limit=3)
    print(f"找到 {len(results)} 个结果:")
    
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result.fact}")
        if hasattr(result, 'valid_at') and result.valid_at:
            print(f"     有效期: {result.valid_at}")
    
    # 中心节点搜索
    if results:
        print(f"\n🎯 中心节点搜索 (基于第一个结果):")
        center_node_uuid = results[0].source_node_uuid
        
        reranked_results = await graphiti.search(
            query, 
            center_node_uuid=center_node_uuid,
            limit=3
        )
        
        print(f"重新排序后找到 {len(reranked_results)} 个结果:")
        for i, result in enumerate(reranked_results, 1):
            print(f"  {i}. {result.fact}")
    
    # 节点搜索
    print(f"\n📊 节点搜索:")
    node_query = "图数据库"
    print(f"查询: {node_query}")
    
    node_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
    node_config.limit = 3
    
    node_results = await graphiti._search(
        query=node_query,
        config=node_config
    )
    
    print(f"找到 {len(node_results.nodes)} 个节点:")
    for i, node in enumerate(node_results.nodes, 1):
        summary = node.summary[:80] + "..." if len(node.summary) > 80 else node.summary
        print(f"  {i}. {node.name}: {summary}")


async def demonstrate_advanced_features(graphiti):
    """演示高级功能"""
    
    # 时间范围查询
    print("\n⏰ 时间范围查询:")
    from datetime import timedelta
    
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(hours=1)
    
    time_results = await graphiti.search(
        "OpenAI GPT",
        start_time=start_time,
        end_time=end_time,
        limit=2
    )
    
    print(f"最近1小时内找到 {len(time_results)} 个相关结果")
    
    # 批量添加剧集
    print("\n📦 批量操作演示:")
    batch_episodes = [
        {
            'name': f'批量剧集 {i}',
            'content': f'这是第 {i} 个批量添加的剧集，内容关于人工智能的发展。',
            'type': EpisodeType.text,
            'description': '批量测试数据'
        }
        for i in range(1, 4)
    ]
    
    for episode in batch_episodes:
        await graphiti.add_episode(
            name=episode['name'],
            episode_body=episode['content'],
            source=episode['type'],
            source_description=episode['description'],
            reference_time=datetime.now(timezone.utc)
        )
    
    print(f"✅ 批量添加了 {len(batch_episodes)} 个剧集")
    
    # 搜索统计
    print("\n📈 搜索统计:")
    all_results = await graphiti.search("人工智能", limit=10)
    print(f"关于'人工智能'的搜索结果总数: {len(all_results)}")


if __name__ == "__main__":
    asyncio.run(main())
