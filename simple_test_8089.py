#!/usr/bin/env python3
"""
简化的 FalkorDB + Gemini 测试脚本
在端口 8089 上运行简单的 HTTP 服务器，验证配置
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from aiohttp import web
except ImportError:
    print("❌ 需要安装 aiohttp")
    print("运行: uv add aiohttp")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleTestServer:
    """简化的测试服务器"""
    
    def __init__(self):
        self.app = web.Application()
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_post('/test_config', self.test_config)
    
    async def index(self, request):
        """主页"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FalkorDB + Gemini 配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .panel { border: 1px solid #ddd; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .panel h2 { margin-top: 0; color: #333; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .config-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🚀 FalkorDB + Gemini 配置测试</h1>
    
    <div class="panel">
        <h2>📊 服务器状态</h2>
        <div id="status">检查中...</div>
        <button onclick="checkStatus()">刷新状态</button>
    </div>
    
    <div class="panel">
        <h2>🔧 配置检查</h2>
        <div id="config-check">
            <h3>环境变量检查:</h3>
            <div class="config-item">
                <strong>GOOGLE_API_KEY:</strong> <span id="api-key-status">检查中...</span>
            </div>
            <div class="config-item">
                <strong>GEMINI_MODEL:</strong> <span id="model-status">检查中...</span>
            </div>
            <div class="config-item">
                <strong>FALKORDB_HOST:</strong> <span id="host-status">检查中...</span>
            </div>
            <div class="config-item">
                <strong>FALKORDB_PORT:</strong> <span id="port-status">检查中...</span>
            </div>
        </div>
        <button onclick="testConfig()">测试配置</button>
        <div id="config-result"></div>
    </div>
    
    <div class="panel">
        <h2>📝 实施步骤</h2>
        <ol>
            <li>✅ 创建配置文件和脚本</li>
            <li>✅ 安装必要依赖 (aiohttp)</li>
            <li>✅ 启动测试服务器 (端口 8089)</li>
            <li>⏳ 配置环境变量</li>
            <li>⏳ 启动 FalkorDB 服务</li>
            <li>⏳ 运行完整测试</li>
        </ol>
        
        <h3>🔗 相关文件:</h3>
        <ul>
            <li><code>falkordb_gemini_config.py</code> - 配置管理模块</li>
            <li><code>falkordb_gemini_example.py</code> - 完整使用示例</li>
            <li><code>.env.falkordb_gemini</code> - 环境变量模板</li>
            <li><code>install_falkordb_gemini.py</code> - 安装脚本</li>
            <li><code>FalkorDB_Gemini_实施指南.md</code> - 详细文档</li>
        </ul>
    </div>
    
    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();
                const statusDiv = document.getElementById('status');
                if (data.status === 'running') {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ 测试服务器运行正常<br>🌐 端口: 8089<br>⏰ 启动时间: ' + data.start_time;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ 服务器状态异常';
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ 无法连接到服务器';
            }
        }
        
        async function testConfig() {
            try {
                const response = await fetch('/test_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                
                // 更新各个状态
                document.getElementById('api-key-status').innerHTML = data.api_key ? '✅ 已设置' : '❌ 未设置';
                document.getElementById('model-status').innerHTML = data.model || '默认值';
                document.getElementById('host-status').innerHTML = data.host || '默认值';
                document.getElementById('port-status').innerHTML = data.port || '默认值';
                
                // 显示详细结果
                const resultDiv = document.getElementById('config-result');
                if (data.ready) {
                    resultDiv.innerHTML = '<div class="result" style="background: #d4edda;">✅ 配置检查通过！可以运行完整测试。</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result" style="background: #f8d7da;">⚠️ 配置不完整。请检查环境变量设置。</div>';
                }
                
            } catch (error) {
                document.getElementById('config-result').innerHTML = '<div class="result" style="background: #f8d7da;">❌ 配置检查失败</div>';
            }
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
            testConfig();
        };
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def status(self, request):
        """状态检查"""
        return web.json_response({
            'status': 'running',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'port': 8089,
            'message': '测试服务器运行正常'
        })
    
    async def test_config(self, request):
        """测试配置"""
        try:
            # 检查环境变量
            api_key = os.getenv('GOOGLE_API_KEY')
            model = os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')
            host = os.getenv('FALKORDB_HOST', 'localhost')
            port = os.getenv('FALKORDB_PORT', '6379')
            
            # 检查配置文件是否存在
            config_files = [
                'falkordb_gemini_config.py',
                '.env.falkordb_gemini',
                'falkordb_gemini_example.py'
            ]
            
            files_exist = []
            for file in config_files:
                file_path = Path(__file__).parent / file
                files_exist.append(file_path.exists())
            
            ready = api_key is not None and all(files_exist)
            
            return web.json_response({
                'api_key': api_key is not None,
                'model': model,
                'host': host,
                'port': port,
                'files_exist': files_exist,
                'ready': ready,
                'message': '配置检查完成' if ready else '需要设置 GOOGLE_API_KEY 环境变量'
            })
            
        except Exception as e:
            logger.error(f"配置检查失败: {e}")
            return web.json_response({
                'error': str(e),
                'ready': False
            })

async def main():
    """主函数"""
    logger.info("🚀 启动简化测试服务器 (端口 8089)")
    
    # 创建服务器实例
    server = SimpleTestServer()
    
    try:
        # 启动 Web 服务器
        runner = web.AppRunner(server.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', 8089)
        await site.start()
        
        logger.info("✅ 测试服务器已启动")
        logger.info("🌐 访问地址: http://localhost:8089")
        logger.info("📋 使用 Ctrl+C 停止服务器")
        
        # 显示配置状态
        api_key = os.getenv('GOOGLE_API_KEY')
        if api_key:
            logger.info("✅ 检测到 GOOGLE_API_KEY")
        else:
            logger.warning("⚠️  未检测到 GOOGLE_API_KEY，请设置环境变量")
        
        # 保持服务器运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器错误: {e}")
    finally:
        logger.info("✅ 服务器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
